<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单Three.js测试</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #f43a47;
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        
        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        canvas {
            display: block;
        }
        
        .info {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            background: rgba(0,0,0,0.7);
            padding: 15px;
            border-radius: 8px;
            z-index: 100;
        }
        
        .controls {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 100;
        }
        
        button {
            background: #000;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        
        button:hover {
            background: #333;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 18px;
            z-index: 50;
        }
    </style>
</head>
<body>
    <div id="container">
        <div class="loading" id="loading">加载Three.js...</div>
        
        <div class="info">
            <h3>Three.js 测试</h3>
            <p>移动鼠标控制视角</p>
            <p>点击按钮切换场景</p>
        </div>
        
        <div class="controls">
            <button onclick="createCube()">立方体</button>
            <button onclick="createSphere()">球体</button>
            <button onclick="createParticles()">粒子</button>
        </div>
    </div>

    <!-- Three.js 核心库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r158/three.min.js"></script>
    
    <script>
        let scene, camera, renderer;
        let currentObject = null;
        let mouseX = 0, mouseY = 0;
        
        function init() {
            console.log('初始化Three.js...');
            
            // 检查Three.js是否加载成功
            if (typeof THREE === 'undefined') {
                document.getElementById('loading').textContent = 'Three.js加载失败！';
                return;
            }
            
            // 创建场景
            scene = new THREE.Scene();
            
            // 创建相机
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.z = 5;
            
            // 创建渲染器
            renderer = new THREE.WebGLRenderer({ alpha: true, antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setClearColor(0x000000, 0);
            document.getElementById('container').appendChild(renderer.domElement);
            
            // 添加光源
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            scene.add(ambientLight);
            
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(10, 10, 5);
            scene.add(directionalLight);
            
            // 鼠标控制
            document.addEventListener('mousemove', onMouseMove);
            
            // 创建默认立方体
            createCube();
            
            // 隐藏加载提示
            document.getElementById('loading').style.display = 'none';
            
            // 开始渲染
            animate();
            
            console.log('Three.js初始化完成！');
        }
        
        function onMouseMove(event) {
            mouseX = (event.clientX - window.innerWidth / 2) * 0.005;
            mouseY = (event.clientY - window.innerHeight / 2) * 0.005;
        }
        
        function createCube() {
            clearScene();
            
            const geometry = new THREE.BoxGeometry(2, 2, 2);
            const material = new THREE.MeshPhongMaterial({ 
                color: 0x000000,
                transparent: true,
                opacity: 0.8
            });
            
            currentObject = new THREE.Mesh(geometry, material);
            scene.add(currentObject);
            
            console.log('创建立方体');
        }
        
        function createSphere() {
            clearScene();
            
            const geometry = new THREE.SphereGeometry(1.5, 32, 32);
            const material = new THREE.MeshPhongMaterial({ 
                color: 0x000000,
                transparent: true,
                opacity: 0.8
            });
            
            currentObject = new THREE.Mesh(geometry, material);
            scene.add(currentObject);
            
            console.log('创建球体');
        }
        
        function createParticles() {
            clearScene();
            
            const particleCount = 500;
            const geometry = new THREE.BufferGeometry();
            const positions = new Float32Array(particleCount * 3);
            const colors = new Float32Array(particleCount * 3);
            
            for (let i = 0; i < particleCount; i++) {
                const i3 = i * 3;
                
                positions[i3] = (Math.random() - 0.5) * 10;
                positions[i3 + 1] = (Math.random() - 0.5) * 10;
                positions[i3 + 2] = (Math.random() - 0.5) * 10;
                
                const color = new THREE.Color();
                color.setHSL(Math.random(), 0.7, 0.6);
                colors[i3] = color.r;
                colors[i3 + 1] = color.g;
                colors[i3 + 2] = color.b;
            }
            
            geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
            geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
            
            const material = new THREE.PointsMaterial({
                size: 0.1,
                vertexColors: true,
                transparent: true,
                opacity: 0.8
            });
            
            currentObject = new THREE.Points(geometry, material);
            scene.add(currentObject);
            
            console.log('创建粒子系统');
        }
        
        function clearScene() {
            if (currentObject) {
                scene.remove(currentObject);
                if (currentObject.geometry) currentObject.geometry.dispose();
                if (currentObject.material) currentObject.material.dispose();
                currentObject = null;
            }
        }
        
        function animate() {
            requestAnimationFrame(animate);
            
            // 相机跟随鼠标
            camera.position.x += (mouseX - camera.position.x) * 0.05;
            camera.position.y += (-mouseY - camera.position.y) * 0.05;
            camera.lookAt(0, 0, 0);
            
            // 旋转当前对象
            if (currentObject) {
                currentObject.rotation.x += 0.01;
                currentObject.rotation.y += 0.01;
            }
            
            renderer.render(scene, camera);
        }
        
        // 窗口大小调整
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });
        
        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            setTimeout(init, 100);
        });
    </script>
</body>
</html>
