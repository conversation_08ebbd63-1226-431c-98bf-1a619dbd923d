# Three.js 使用指南

## 🚀 **如何引入Three.js**

### 1. **CDN方式（推荐）**
```html
<!-- 核心库 -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r158/three.min.js"></script>

<!-- 扩展库 -->
<script src="https://cdn.jsdelivr.net/npm/three@0.158.0/examples/js/controls/OrbitControls.js"></script>
<script src="https://cdn.jsdelivr.net/npm/three@0.158.0/examples/js/loaders/GLTFLoader.js"></script>
```

### 2. **从官网下载**
- 访问：https://threejs.org/
- 点击 "Download" 按钮
- 下载最新版本的压缩包
- 解压后引入 `build/three.min.js`

### 3. **NPM安装**
```bash
npm install three
```

## 📚 **官网资源获取**

### **官方网站**
- 主站：https://threejs.org/
- 文档：https://threejs.org/docs/
- 示例：https://threejs.org/examples/
- 编辑器：https://threejs.org/editor/

### **从官网复制代码**
1. **浏览示例**：https://threejs.org/examples/
2. **查看源码**：点击示例右下角的 `< >` 按钮
3. **复制代码**：直接复制需要的部分
4. **查看文档**：https://threejs.org/docs/ 获取API详情

## 🎯 **基础使用模板**

### **最简单的Three.js场景**
```javascript
// 1. 创建场景
const scene = new THREE.Scene();

// 2. 创建相机
const camera = new THREE.PerspectiveCamera(
    75,                                    // 视野角度
    window.innerWidth / window.innerHeight, // 宽高比
    0.1,                                   // 近裁剪面
    1000                                   // 远裁剪面
);

// 3. 创建渲染器
const renderer = new THREE.WebGLRenderer();
renderer.setSize(window.innerWidth, window.innerHeight);
document.body.appendChild(renderer.domElement);

// 4. 创建几何体和材质
const geometry = new THREE.BoxGeometry(1, 1, 1);
const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
const cube = new THREE.Mesh(geometry, material);

// 5. 添加到场景
scene.add(cube);

// 6. 设置相机位置
camera.position.z = 5;

// 7. 渲染循环
function animate() {
    requestAnimationFrame(animate);
    
    // 旋转立方体
    cube.rotation.x += 0.01;
    cube.rotation.y += 0.01;
    
    renderer.render(scene, camera);
}
animate();
```

## 🎨 **常用几何体**

```javascript
// 立方体
const boxGeometry = new THREE.BoxGeometry(1, 1, 1);

// 球体
const sphereGeometry = new THREE.SphereGeometry(1, 32, 32);

// 平面
const planeGeometry = new THREE.PlaneGeometry(1, 1);

// 圆柱体
const cylinderGeometry = new THREE.CylinderGeometry(1, 1, 1, 32);

// 圆锥体
const coneGeometry = new THREE.ConeGeometry(1, 1, 32);

// 环形
const torusGeometry = new THREE.TorusGeometry(1, 0.4, 16, 100);
```

## 🎭 **常用材质**

```javascript
// 基础材质（不受光照影响）
const basicMaterial = new THREE.MeshBasicMaterial({ color: 0xff0000 });

// 朗伯材质（受光照影响）
const lambertMaterial = new THREE.MeshLambertMaterial({ color: 0xff0000 });

// Phong材质（有高光）
const phongMaterial = new THREE.MeshPhongMaterial({ color: 0xff0000 });

// 标准材质（PBR）
const standardMaterial = new THREE.MeshStandardMaterial({ color: 0xff0000 });

// 线框材质
const wireframeMaterial = new THREE.MeshBasicMaterial({ 
    color: 0xff0000, 
    wireframe: true 
});
```

## 💡 **光源类型**

```javascript
// 环境光（全局照明）
const ambientLight = new THREE.AmbientLight(0x404040, 0.6);

// 方向光（太阳光）
const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
directionalLight.position.set(10, 10, 5);

// 点光源
const pointLight = new THREE.PointLight(0xff0000, 1, 100);
pointLight.position.set(10, 10, 10);

// 聚光灯
const spotLight = new THREE.SpotLight(0xffffff, 1);
spotLight.position.set(100, 1000, 100);
```

## 🎮 **交互控制**

```javascript
// 轨道控制器（需要引入OrbitControls.js）
const controls = new THREE.OrbitControls(camera, renderer.domElement);
controls.enableDamping = true;
controls.dampingFactor = 0.05;

// 在渲染循环中更新
function animate() {
    requestAnimationFrame(animate);
    controls.update();
    renderer.render(scene, camera);
}
```

## 🌟 **高级功能**

### **粒子系统**
```javascript
const particleCount = 1000;
const geometry = new THREE.BufferGeometry();
const positions = new Float32Array(particleCount * 3);

for (let i = 0; i < particleCount * 3; i++) {
    positions[i] = (Math.random() - 0.5) * 10;
}

geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
const material = new THREE.PointsMaterial({ color: 0xff0000, size: 0.1 });
const particles = new THREE.Points(geometry, material);
scene.add(particles);
```

### **纹理加载**
```javascript
const textureLoader = new THREE.TextureLoader();
const texture = textureLoader.load('path/to/texture.jpg');
const material = new THREE.MeshBasicMaterial({ map: texture });
```

### **3D模型加载**
```javascript
const loader = new THREE.GLTFLoader();
loader.load('path/to/model.gltf', (gltf) => {
    scene.add(gltf.scene);
});
```

## 🔧 **性能优化**

1. **合理使用几何体复用**
2. **减少draw calls**
3. **使用LOD（细节层次）**
4. **合理设置相机裁剪面**
5. **使用Object3D.frustumCulled**
6. **避免频繁创建/销毁对象**

## 📱 **响应式处理**

```javascript
window.addEventListener('resize', () => {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
});
```

## 🎯 **项目中的实际应用**

在我们的项目中，第三页使用了Four种不同的3D场景：

1. **几何艺术**：组合几何体展示
2. **粒子宇宙**：粒子系统效果
3. **变形动画**：几何体变形
4. **星系漩涡**：螺旋粒子效果

每个场景都有独特的视觉效果和交互体验！

## 🔗 **有用的链接**

- **Three.js官网**：https://threejs.org/
- **Three.js文档**：https://threejs.org/docs/
- **Three.js示例**：https://threejs.org/examples/
- **Three.js GitHub**：https://github.com/mrdoob/three.js/
- **Three.js论坛**：https://discourse.threejs.org/
