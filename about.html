<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About - Personal Portfolio Demo</title>
    <link rel="stylesheet" href="css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 移动端警告 -->
    <div class="mobile-warning">
        <div class="mobile-warning-text">
            This webpage is better accessed through a computer browser.
        </div>
    </div>

    <!-- 自定义光标 -->
    <div class="custom-cursor"></div>

    <!-- 主内容 -->
    <div class="main-content">
        <!-- 导航栏 -->
        <nav class="navbar">
            <div class="nav-logo">
                <span class="logo-text">PORTFOLIO</span>
            </div>
            <div class="nav-links">
                <a href="index.html" class="nav-link">HOME</a>
                <a href="#about" class="nav-link active">ABOUT</a>
            </div>
        </nav>

        <!-- 关于页面内容 -->
        <section class="about-hero" id="about">
            <div class="about-background">
                <div class="background-text">ABOUT</div>
                <div class="background-text-2">ME</div>
            </div>
            
            <div class="about-content">
                <div class="about-intro">
                    <h1 class="about-title">
                        <span class="title-line">PASSIONATE</span>
                        <span class="title-line highlight">DEVELOPER</span>
                    </h1>
                    <p class="about-description">
                        I'm a creative developer with a passion for building beautiful, 
                        functional, and user-centered digital experiences. With expertise 
                        in modern web technologies, I bring ideas to life through code.
                    </p>
                </div>
            </div>
        </section>

        <!-- 技能区域 -->
        <section class="skills-section">
            <div class="section-header">
                <h2 class="section-title">SKILLS & EXPERTISE</h2>
                <div class="section-line"></div>
            </div>
            
            <div class="skills-grid">
                <div class="skill-category">
                    <h3 class="skill-title">Frontend</h3>
                    <div class="skill-list">
                        <div class="skill-item">
                            <span class="skill-name">JavaScript</span>
                            <div class="skill-bar">
                                <div class="skill-progress" data-progress="90"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <span class="skill-name">React</span>
                            <div class="skill-bar">
                                <div class="skill-progress" data-progress="85"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <span class="skill-name">CSS/SCSS</span>
                            <div class="skill-bar">
                                <div class="skill-progress" data-progress="95"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="skill-category">
                    <h3 class="skill-title">Backend</h3>
                    <div class="skill-list">
                        <div class="skill-item">
                            <span class="skill-name">Node.js</span>
                            <div class="skill-bar">
                                <div class="skill-progress" data-progress="80"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <span class="skill-name">Python</span>
                            <div class="skill-bar">
                                <div class="skill-progress" data-progress="75"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <span class="skill-name">Database</span>
                            <div class="skill-bar">
                                <div class="skill-progress" data-progress="70"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="skill-category">
                    <h3 class="skill-title">Design</h3>
                    <div class="skill-list">
                        <div class="skill-item">
                            <span class="skill-name">UI/UX Design</span>
                            <div class="skill-bar">
                                <div class="skill-progress" data-progress="85"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <span class="skill-name">Figma</span>
                            <div class="skill-bar">
                                <div class="skill-progress" data-progress="80"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <span class="skill-name">Adobe Creative</span>
                            <div class="skill-bar">
                                <div class="skill-progress" data-progress="75"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 经历时间线 -->
        <section class="timeline-section">
            <div class="section-header">
                <h2 class="section-title">EXPERIENCE</h2>
                <div class="section-line"></div>
            </div>
            
            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-date">2023 - Present</div>
                    <div class="timeline-content">
                        <h3 class="timeline-title">Senior Frontend Developer</h3>
                        <p class="timeline-company">Tech Company Inc.</p>
                        <p class="timeline-description">
                            Leading frontend development for multiple web applications, 
                            mentoring junior developers, and implementing modern development practices.
                        </p>
                    </div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-date">2021 - 2023</div>
                    <div class="timeline-content">
                        <h3 class="timeline-title">Full Stack Developer</h3>
                        <p class="timeline-company">Digital Agency</p>
                        <p class="timeline-description">
                            Developed and maintained web applications using React, Node.js, 
                            and various databases. Collaborated with design teams to create 
                            pixel-perfect user interfaces.
                        </p>
                    </div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-date">2019 - 2021</div>
                    <div class="timeline-content">
                        <h3 class="timeline-title">Junior Developer</h3>
                        <p class="timeline-company">Startup Studio</p>
                        <p class="timeline-description">
                            Started my professional journey building responsive websites 
                            and learning modern web development technologies.
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 联系区域 -->
        <section class="contact-section">
            <div class="contact-content">
                <h2 class="contact-title">GET IN TOUCH</h2>
                <p class="contact-subtitle">Let's discuss your next project</p>
                <div class="contact-buttons">
                    <a href="mailto:<EMAIL>" class="contact-button">SEND MESSAGE</a>
                    <a href="index.html" class="contact-button secondary">BACK TO HOME</a>
                </div>
            </div>
        </section>
    </div>

    <script src="js/main.js"></script>
</body>
</html>
