<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Personal Portfolio - Demo</title>
    <link rel="stylesheet" href="css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
    <!-- Three.js 核心库 - 使用更稳定的CDN -->
    <script src="https://unpkg.com/three@0.158.0/build/three.min.js"></script>
</head>
<body>
    <!-- 移动端警告 -->
    <div class="mobile-warning">
        <div class="mobile-warning-text">
            This webpage is better accessed through a computer browser.
        </div>
    </div>

    <!-- 自定义光标 -->
    <div class="custom-cursor"></div>

    <!-- 加载动画 -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-container">
            <div class="loading-logo">
                <div class="loading-text">LOADING</div>
                <div class="loading-bar">
                    <div class="loading-progress" id="loading-progress"></div>
                </div>
                <div class="loading-percentage" id="loading-percentage">0%</div>
            </div>
        </div>
    </div>

    <!-- 相机取景框 -->
    <div class="camera-frame">
        <div class="frame-corner top-left"></div>
        <div class="frame-corner top-right"></div>
        <div class="frame-corner bottom-left"></div>
        <div class="frame-corner bottom-right"></div>
    </div>

    <!-- 主内容 -->
    <div class="main-content" id="main-content">
        <!-- 导航栏 -->
        <nav class="navbar">
            <div class="nav-logo">
                <div class="morphing-logo" id="morphing-logo">
                    <svg viewBox="0 0 200 100" class="logo-svg" id="svg-logo">
                        <path id="half-circle" d="M50,50 A25,25 0 0,1 100,50" stroke="#000" stroke-width="3" fill="none"/>
                        <path id="rect1" d="M120,30 L180,30 L180,70 L120,70 Z" stroke="#000" stroke-width="3" fill="none"/>
                        <path id="line1" d="M20,20 L40,80" stroke="#000" stroke-width="3" fill="none"/>
                        <path id="line2" d="M40,20 L20,80" stroke="#000" stroke-width="3" fill="none"/>
                        <circle id="circle" cx="160" cy="50" r="8" stroke="#000" stroke-width="3" fill="none"/>
                    </svg>
                </div>
                <span class="logo-text">X OBERON</span>
            </div>
            <div class="nav-links">
                <a href="#page1" class="nav-link active">HOME</a>
                <a href="#page2" class="nav-link">ABOUT</a>
            </div>
        </nav>

        <!-- 右侧页面导航 -->
        <nav class="page-navigation">
            <div class="nav-dot active" data-page="HOME"></div>
            <div class="nav-dot" data-page="ABOUT"></div>
            <div class="nav-dot" data-page="IDENTITY"></div>
            <div class="nav-dot" data-page="CREATOR"></div>
            <div class="nav-dot" data-page="DEVELOPER"></div>
        </nav>

        <!-- 滚动提示 -->
        <div class="scroll-hint">
            <div class="scroll-arrow"></div>
            <div class="scroll-text">SCROLL</div>
        </div>

        <!-- 滚动容器 -->
        <div class="scroll-container" id="scroll-container">

        <!-- 第1页 - 主页内容 -->
        <section class="page-section hero-section active-section" id="page1">
            <!-- GitHub链接 - 右上角 -->
            <div class="github-link-container">
                <a href="https://github.com/yourusername" target="_blank" class="github-link">
                    <svg class="github-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                    </svg>
                    <span class="github-text">GITHUB</span>
                </a>
            </div>

            <div class="hero-background">
                <!-- 简化的X OBERON水印 -->
                <div class="watermark-text watermark-1">X OBERON</div>
                <div class="watermark-text watermark-2">X OBERON</div>
                <div class="watermark-text watermark-3">X OBERON</div>
            </div>
            
            <div class="hero-content">
                <div class="hero-title">
                    <h1 class="main-title">
                        <span class="title-line">HELLO</span>
                        <span class="title-line">I'M</span>
                        <span class="title-line highlight">X OBERON</span>
                    </h1>
                </div>
                
                <div class="hero-subtitle">
                    <p>Creating digital experiences with passion and precision</p>
                </div>
                
                <div class="hero-cta">
                    <button class="cta-button" onclick="scrollToSection('projects')">
                        VIEW MY WORK
                    </button>
                </div>
            </div>

            <div class="scroll-indicator">
                <div class="scroll-line"></div>
                <span class="scroll-text">SCROLL</span>
            </div>
        </section>

        <!-- 第2页 - 关于页面 -->
        <section class="page-section" id="page2">
            <!-- 左半部分 - 文字内容 -->
            <div class="about-left-section">
                <div class="about-content">
                    <div class="about-title">X Oberon</div>
                    <div class="about-description">
                        <p class="about-line">
                            <span class="normal">Creative developer specializing in</span>
                            <span class="highlight">Interactive Design</span>
                            <span class="highlight">Web Development</span>
                            <span class="normal">and</span>
                            <span class="highlight">Digital Art</span>
                        </p>
                        <div class="about-paragraph">
                            Passionate creator committed to pushing the boundaries of digital experiences.
                            Building immersive web applications that blend technology with artistry.
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右半部分 - 贴纸装饰 -->
            <div class="about-right-section">
                <div class="sticker-decorations">
                    <!-- 贴纸组1 - 左上角 -->
                    <div class="sticker-group sticker-group-1">
                        <div class="sticker-pin"></div>
                        <div class="sticker sticker-polaroid sticker-xl">
                            <div class="sticker-content">
                                <div class="sticker-image">XO</div>
                                <div class="sticker-label">CREATOR</div>
                            </div>
                        </div>
                    </div>

                    <!-- 贴纸组2 - 右上角 -->
                    <div class="sticker-group sticker-group-2">
                        <div class="sticker-pin"></div>
                        <div class="sticker sticker-badge sticker-xl">
                            <div class="sticker-text">DEVELOPER</div>
                        </div>
                    </div>

                    <!-- 贴纸组3 - 中央 -->
                    <div class="sticker-group sticker-group-3">
                        <div class="sticker-pin"></div>
                        <div class="sticker sticker-polaroid sticker-xxl">
                            <div class="sticker-content">
                                <div class="sticker-image">WEB</div>
                                <div class="sticker-label">DESIGNER</div>
                            </div>
                        </div>
                    </div>

                    <!-- 贴纸组4 - 左下角 -->
                    <div class="sticker-group sticker-group-4">
                        <div class="sticker-pin"></div>
                        <div class="sticker sticker-tag sticker-xl">
                            <div class="sticker-text">CREATIVE</div>
                        </div>
                    </div>

                    <!-- 贴纸组5 - 右下角 -->
                    <div class="sticker-group sticker-group-5">
                        <div class="sticker-pin"></div>
                        <div class="sticker sticker-polaroid sticker-xl sticker-tilted">
                            <div class="sticker-content">
                                <div class="sticker-image">UI</div>
                                <div class="sticker-label">ARTIST</div>
                            </div>
                        </div>
                    </div>


                </div>
            </div>
        </section>

        <!-- 第3页 - 水波游泳池效果 -->
        <section class="page-section" id="page3">
            <!-- Three.js 水波容器 -->
            <div class="water-container" id="water-container">
                <canvas id="water-canvas" class="water-canvas"></canvas>

                <!-- 水波上的标题内容 -->
                <div class="water-overlay-content">
                    <div class="water-title">
                        <h1 class="water-main-title">
                            <span class="water-title-line">FLOW</span>
                            <span class="water-title-line">WITH</span>
                            <span class="water-title-line water-highlight">PASSION</span>
                        </h1>
                    </div>
                    <div class="water-subtitle">
                        <p>Interactive Liquid Art Experience</p>
                    </div>
                </div>
            </div>


        </section>

        <!-- 第4页 - 创作者 (物理小球掉落效果) -->
        <section class="page-section" id="page4">
            <!-- 物理小球画布 -->
            <canvas id="physics-canvas" class="physics-canvas"></canvas>

            <!-- 主要内容 -->
            <div class="hero-content">
                <div class="hero-title">
                    <h1 class="main-title">
                        <span class="title-line">KEEP</span>
                        <span class="title-line highlight">CREATING</span>
                        <span class="title-line">MAGIC</span>
                    </h1>
                </div>

                <div class="hero-subtitle">
                    <p>Interactive Creative Experience</p>
                </div>
            </div>
        </section>

        <!-- 第5页 - 现代化堆叠卡片效果 -->
        <section class="page-section" id="page5">
            <!-- 页面标题 -->
            <div class="page-header">
                <h1 class="page-title">Interactive Stacked Cards</h1>
                <p class="page-subtitle">Hover over the cards to view detailed information in the right panel</p>
            </div>

            <!-- 主要内容区域 - 左右分栏 -->
            <div class="cards-main-container">
                <!-- 左侧 - 堆叠卡片区域 -->
                <div class="cards-left-panel">
                    <div class="stacked-cards-container">
                        <!-- 第一行第一列 - Walkman 堆叠 -->
                        <div class="card-stack-cell">
                            <div class="stacked-card" data-id="walkman" data-index="0">
                                <div class="card-tab-label">
                                    <span class="card-number">010</span>
                                    <span class="card-title">walkman - 1979</span>
                                </div>
                                <div class="category-indicator">S</div>
                                <div class="card-content">
                                    <div class="card-header">
                                        <h3 class="card-title-main">Walkman</h3>
                                        <p class="card-subtitle">vintage audio</p>
                                    </div>
                                </div>
                            </div>
                            <!-- 堆叠效果的背景卡片 -->
                            <div class="stacked-card stack-bg"></div>
                            <div class="stacked-card stack-bg"></div>
                        </div>

                        <!-- 第一行第二列 - Oil Lamp 堆叠 -->
                        <div class="card-stack-cell">
                            <div class="stacked-card" data-id="oil-lamp" data-index="1">
                                <div class="card-tab-label">
                                    <span class="card-number">94</span>
                                    <span class="card-title">oil lamp</span>
                                </div>
                                <div class="category-indicator">O</div>
                                <div class="card-content">
                                    <div class="card-header">
                                        <h3 class="card-title-main">Oil Lamp</h3>
                                        <p class="card-subtitle">vintage lighting</p>
                                    </div>
                                </div>
                            </div>
                            <!-- 堆叠效果的背景卡片 -->
                            <div class="stacked-card stack-bg"></div>
                            <div class="stacked-card stack-bg"></div>
                        </div>

                        <!-- 第一行第三列 - Oats 堆叠 -->
                        <div class="card-stack-cell">
                            <div class="stacked-card" data-id="oats" data-index="2">
                                <div class="card-tab-label">
                                    <span class="card-number">95</span>
                                    <span class="card-title">oats</span>
                                </div>
                                <div class="category-indicator">O</div>
                                <div class="card-content">
                                    <div class="card-header">
                                        <h3 class="card-title-main">Oats</h3>
                                        <p class="card-subtitle">breakfast cereal</p>
                                    </div>

                                </div>
                            </div>
                            <!-- 堆叠效果的背景卡片 -->
                            <div class="stacked-card stack-bg"></div>
                            <div class="stacked-card stack-bg"></div>
                        </div>

                        <!-- 第二行第一列 - Jeans 堆叠 -->
                        <div class="card-stack-cell">
                            <div class="stacked-card" data-id="pants" data-index="3">
                                <div class="card-tab-label">
                                    <span class="card-number">96</span>
                                    <span class="card-title">pants</span>
                                </div>
                                <div class="category-indicator">P</div>
                                <div class="card-content">
                                    <div class="card-header">
                                        <h3 class="card-title-main">Jeans</h3>
                                        <p class="card-subtitle">clothing item</p>
                                    </div>

                                </div>
                            </div>
                            <!-- 堆叠效果的背景卡片 -->
                            <div class="stacked-card stack-bg"></div>
                            <div class="stacked-card stack-bg"></div>
                        </div>

                        <!-- 第二行第二列 - Airplane 堆叠 -->
                        <div class="card-stack-cell">
                            <div class="stacked-card" data-id="plane" data-index="4">
                                <div class="card-tab-label">
                                    <span class="card-number">97</span>
                                    <span class="card-title">plane</span>
                                </div>
                                <div class="category-indicator">P</div>
                                <div class="card-content">
                                    <div class="card-header">
                                        <h3 class="card-title-main">Airplane</h3>
                                        <p class="card-subtitle">aircraft</p>
                                    </div>

                                </div>
                            </div>
                            <!-- 堆叠效果的背景卡片 -->
                            <div class="stacked-card stack-bg"></div>
                            <div class="stacked-card stack-bg"></div>
                        </div>

                        <!-- 第二行第三列 - Plant 堆叠 -->
                        <div class="card-stack-cell">
                            <div class="stacked-card" data-id="plant" data-index="5">
                                <div class="card-tab-label">
                                    <span class="card-number">98</span>
                                    <span class="card-title">plant 003</span>
                                </div>
                                <div class="category-indicator">P</div>
                                <div class="card-content">
                                    <div class="card-header">
                                        <h3 class="card-title-main">Plant</h3>
                                        <p class="card-subtitle">botanical specimen</p>
                                    </div>

                                </div>
                            </div>
                            <!-- 堆叠效果的背景卡片 -->
                            <div class="stacked-card stack-bg"></div>
                            <div class="stacked-card stack-bg"></div>
                        </div>
                    </div>
                </div>

                <!-- 右侧 - 详情面板 -->
                <div class="cards-right-panel">
                    <div class="detail-panel">
                        <!-- 默认状态 -->
                        <div class="detail-placeholder" id="detail-placeholder">
                            <div class="placeholder-icon">
                                <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                    <path d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                </svg>
                            </div>
                            <h3>Hover over a Card</h3>
                            <p>Hover over any card from the left to view detailed information here.</p>
                        </div>

                        <!-- 详情内容 -->
                        <div class="detail-content" id="detail-content" style="display: none;">
                            <div class="detail-header">
                                <div class="detail-category-indicator" id="detail-category"></div>
                                <div class="detail-title-section">
                                    <h2 class="detail-title" id="detail-title"></h2>
                                    <p class="detail-subtitle" id="detail-subtitle"></p>
                                    <p class="detail-number" id="detail-number"></p>
                                </div>
                            </div>

                            <div class="detail-main">
                                <div class="detail-color-card-container">
                                    <div id="detail-color-card" class="detail-color-card">
                                        <!-- 色卡内容将通过JavaScript动态更新 -->
                                    </div>
                                </div>

                                <div class="detail-description">
                                    <h3>Description</h3>
                                    <p id="detail-description-text"></p>
                                </div>

                                <div class="detail-specs">
                                    <h3>Details</h3>
                                    <div class="specs-list" id="detail-specs-list">
                                        <!-- 动态生成的规格信息 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <script src="js/main.js"></script>
</body>
</html>
