# 变形交互翻页效果技术文档

## 概述
这是一个基于滚动的多页面交互式网站，具有SVG Logo变形动画、页面过渡效果和多种交互元素。主要特色是Logo在页面滚动时的平滑变形和位置变化。

## 核心技术架构

### 1. 页面结构
```html
<!-- 主要容器结构 -->
<div class="scroll-container" id="scroll-container">
    <section class="page-section" id="page1">...</section>
    <section class="page-section" id="page2">...</section>
    <!-- 更多页面... -->
</div>

<!-- 变形Logo容器 -->
<div id="morphing-logo">
    <svg id="svg-logo">
        <path id="half-circle">...</path>
        <path id="rect1">...</path>
        <!-- 更多SVG元素... -->
    </svg>
</div>
```

### 2. CSS 滚动配置
```css
.scroll-container {
    height: 100vh;
    overflow-y: auto;
    scroll-snap-type: y mandatory;  /* 页面吸附效果 */
    -webkit-overflow-scrolling: touch;
}

.page-section {
    width: 100%;
    height: 100vh;
    scroll-snap-align: start;  /* 每页对齐到顶部 */
    position: relative;
    overflow: hidden;
}
```

## 核心功能实现

### 1. SVG路径插值算法
```javascript
// SVG路径解析
function parsePath(pathString) {
    const commands = [];
    const regex = /([MLHVCSQTAZ])\s*([^MLHVCSQTAZ]*)/gi;
    let match;
    
    while ((match = regex.exec(pathString)) !== null) {
        const type = match[1].toUpperCase();
        const values = match[2].trim().split(/[\s,]+/)
            .filter(v => v !== '')
            .map(Number);
        commands.push({ type, values });
    }
    return commands;
}

// 路径插值计算
function interpolatePath(path1, path2, progress) {
    const commands1 = parsePath(path1);
    const commands2 = parsePath(path2);
    
    if (commands1.length !== commands2.length) {
        return progress < 0.5 ? path1 : path2;
    }
    
    const resultCommands = [];
    for (let i = 0; i < commands1.length; i++) {
        const cmd1 = commands1[i];
        const cmd2 = commands2[i];
        
        const interpolatedValues = cmd1.values.map((val, j) => 
            val + (cmd2.values[j] - val) * progress
        );
        
        resultCommands.push({
            type: cmd1.type,
            values: interpolatedValues
        });
    }
    
    return rebuildPath(resultCommands);
}
```

### 2. Logo变形动画系统
```javascript
// Logo样式配置
const logoCSS = {
    big: { 
        left: '52vw', 
        height: '36vh', 
        top: '2vh', 
        transform: 'translateX(-50%)' 
    },
    small: { 
        left: '2.5vw', 
        height: '6vh', 
        top: '1vh', 
        transform: 'translateX(-50%)' 
    }
};

// Logo位置和大小更新
function updateLogoPositionAndSize(progress) {
    const left = interpolateValue(logoCSS.big.left, logoCSS.small.left, progress);
    const height = interpolateValue(logoCSS.big.height, logoCSS.small.height, progress);
    const top = interpolateValue(logoCSS.big.top, logoCSS.small.top, progress);
    const translateX = -50 + (0 - (-50)) * progress;
    
    morphingLogo.style.left = left;
    morphingLogo.style.top = top;
    morphingLogo.style.height = height;
    morphingLogo.style.transform = `translateX(${translateX}%)`;
}

// SVG变形执行
function morphSvg(fromBigToSmall, progress) {
    const source = fromBigToSmall ? window.bigSvgPaths : window.smallSvgPaths;
    const target = fromBigToSmall ? window.smallSvgPaths : window.bigSvgPaths;
    
    // 更新每个SVG元素
    Object.keys(source).forEach(id => {
        const element = document.getElementById(id);
        if (element && target[id]) {
            if (id === 'circle') {
                // 圆形元素特殊处理
                element.setAttribute('cx', source[id].cx + (target[id].cx - source[id].cx) * progress);
                element.setAttribute('cy', source[id].cy + (target[id].cy - source[id].cy) * progress);
                element.setAttribute('r', source[id].r + (target[id].r - source[id].r) * progress);
            } else {
                // 路径元素插值
                const interpolatedPath = interpolatePath(source[id], target[id], progress);
                element.setAttribute('d', interpolatedPath);
            }
        }
    });
}
```

### 3. 滚动事件处理
```javascript
function handleScroll() {
    const scrollPosition = scrollContainer.scrollTop;
    const windowHeight = window.innerHeight;
    
    // 检测当前页面
    sections.forEach((section, index) => {
        const sectionTop = section.offsetTop - 10;
        const sectionBottom = sectionTop + section.offsetHeight;
        
        if (scrollPosition >= sectionTop && scrollPosition < sectionBottom) {
            if (currentIndex !== index) {
                // 更新活动页面
                sections.forEach(s => s.classList.remove('active-section'));
                section.classList.add('active-section');
                currentIndex = index;
            }
        }
    });
    
    // 计算动画进度
    const currentPage = Math.floor(scrollPosition / windowHeight) + 1;
    const currentProgress = (scrollPosition % windowHeight) / windowHeight;
    const page1to2Progress = Math.min(scrollPosition / windowHeight, 1);
    
    // 执行变形动画
    morphSvg(true, page1to2Progress);
    updateLogoPositionAndSize(page1to2Progress);
}

// 绑定滚动监听
scrollContainer.addEventListener('scroll', handleScroll);
```

## 关键特性

### 1. 响应式设计
- 使用 `vw`、`vh` 单位确保跨设备一致性
- `clamp()` 函数实现字体大小自适应
- 媒体查询处理移动端显示

### 2. 性能优化
- 防抖处理窗口大小变化事件
- 条件渲染减少不必要的计算
- 使用 `transform` 而非改变 `left/top` 提升性能

### 3. 交互体验
- `scroll-snap` 实现页面吸附效果
- 自定义光标跟随鼠标移动
- 平滑的过渡动画和缓动函数

## 扩展功能

### 1. 页面特效系统
每个页面可以有独立的初始化逻辑：
```javascript
// 页面4 - Three.js 3D场景
if (currentPage === 4 && currentProgress > 0.6 && !page5_initialized) {
    page5_initialized = true;
    if (window.initialize_page5) window.initialize_page5();
}
```

### 2. 动态内容加载
支持延迟加载和场景切换，提升首屏加载速度。

## 技术栈
- **HTML5**: 语义化结构
- **CSS3**: Flexbox、Grid、动画、变换
- **JavaScript ES6+**: 模块化、异步处理
- **SVG**: 矢量图形和路径动画
- **Three.js**: 3D场景渲染（部分页面）
- **GSAP**: 高性能动画库

## X Oberon 背景文字效果

### 1. 动态背景文字
```css
.WHOAMI {
    position: fixed;
    top: 53vh;
    right: 50vw;
    color: rgba(239, 238, 236, 0);
    font-family: Helvetica, Inter;
    font-size: min(35vh, 23vw);
    font-weight: 700;
    line-height: 270px;
    letter-spacing: -2px;
    text-align: center;
    z-index: -100;
    transform: translate(50%, -50%);
}
```

### 2. 背景文字状态管理
```javascript
const whoamiCSS = {
    invisable: {
        top: '53vh',
        right: '50vw',
        color: 'rgba(239, 238, 236, 0)',
        'font-size': 'min(35vh, 23vw)',
        '-webkit-text-stroke': '0vw'
    },
    visable: {
        top: '50vh',
        right: '40vw',
        color: 'rgba(239, 238, 236, 0.3)',
        'font-size': '23vw',
        '-webkit-text-stroke': '0vw'
    },
    bakground_color: {
        top: '50vh',
        right: '40vw',
        color: 'rgba(239, 238, 236, 1)',
        'font-size': '200vw',
        '-webkit-text-stroke': '200vw rgb(255, 255, 255)'
    }
};

// 根据页面状态更新背景文字
function updateWHOAMICSS(currentPage, currentProgress) {
    const whoami = document.getElementById('WHOAMI');

    if (currentPage >= 2 && currentPage <= 7) {
        // 在页面2-7显示半透明的 "X Oberon"
        Object.keys(whoamiCSS.visable).forEach(prop => {
            whoami.style[prop] = whoamiCSS.visable[prop];
        });
    } else {
        // 其他页面隐藏
        Object.keys(whoamiCSS.invisable).forEach(prop => {
            whoami.style[prop] = whoamiCSS.invisable[prop];
        });
    }
}
```

### 3. 品牌标识系统
- **主标题**: "OhMyKing's Space" - 个人品牌标识
- **副标题**: "@OhMyKing" - 社交媒体标识
- **背景文字**: "X Oberon" - 神秘的背景元素，可能代表某种理念或项目代号

## 高级交互特性

### 1. 动态字母间距调整
```javascript
function adjustLetterSpacing() {
    const elements = document.querySelectorAll('.background-text-ground div[class$="1"], .background-text-ground div[class$="2"], .background-text-ground .background-text');

    elements.forEach(element => {
        const rect = element.getBoundingClientRect();
        const containerWidth = window.innerWidth;
        const textWidth = rect.width;

        if (textWidth > 0) {
            const availableSpace = containerWidth - textWidth;
            const charCount = element.textContent.length;

            if (charCount > 1) {
                const optimalSpacing = Math.max(-2, availableSpace / (charCount - 1));
                element.style.letterSpacing = `${optimalSpacing}px`;
            }
        }
    });
}

// 响应式调整
window.addEventListener('resize', () => {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(adjustLetterSpacing, 250);
});
```

### 2. 自定义光标系统
```javascript
const customCursor = document.querySelector('.custom-cursor');
if (customCursor) {
    window.onmousemove = function(event) {
        customCursor.style.left = event.clientX + "px";
        customCursor.style.top = event.clientY + "px";
    };
}
```

```css
.custom-cursor {
    position: absolute;
    width: 1vw;
    height: 1vw;
    border-radius: 50%;
    background: #fff;
    mix-blend-mode: difference;  /* 反色混合模式 */
    z-index: 2000;
    pointer-events: none;
}
```

### 3. 彩蛋交互系统
```javascript
const coloredEggButton = document.getElementById('colored-egg-button');
let clickCount = 0;
const maxClicks = 5;

coloredEggButton.addEventListener('click', function(event) {
    event.preventDefault();
    event.stopPropagation();

    clickCount++;

    // 点击反馈动画
    coloredEggButton.style.transform = 'scale(1.2)';
    setTimeout(() => coloredEggButton.style.transform = 'scale(1)', 150);

    if (clickCount >= maxClicks) {
        triggerEasterEgg();
    }
});

function triggerEasterEgg() {
    // 显示隐藏的Logo图片
    const eggImages = ['./src/imgs/egg_logo1.png', './src/imgs/egg_logo2.png'];
    const randomImage = eggImages[Math.floor(Math.random() * eggImages.length)];
    // ... 彩蛋逻辑
}
```

## 浏览器兼容性
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

支持现代浏览器的 CSS Grid、Flexbox、SVG 动画等特性。
