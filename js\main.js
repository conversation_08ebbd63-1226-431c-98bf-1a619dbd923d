// ==================== 全局变量 ====================
let isLoading = true;
let loadingProgress = 0;

// ==================== DOM 加载完成后执行 ====================
document.addEventListener('DOMContentLoaded', function() {
    // 注册GSAP插件
    if (typeof gsap !== 'undefined' && typeof ScrollTrigger !== 'undefined') {
        gsap.registerPlugin(ScrollTrigger);
    }
    initializeApp();
});

// ==================== 初始化应用 ====================
function initializeApp() {
    // 初始化自定义光标
    initCustomCursor();
    
    // 开始加载动画
    startLoadingAnimation();

    // 初始化水印系统
    initWatermarkSystem();

    // 初始化滚动驱动动画
    initScrollDrivenAnimations();

    // 初始化页面特定功能
    if (document.getElementById('home')) {
        initHomePage();
    }

    if (document.getElementById('about')) {
        initAboutPage();
    }

    // 添加平滑滚动
    initSmoothScroll();
}

// ==================== 自定义光标 ====================
function initCustomCursor() {
    const cursor = document.querySelector('.custom-cursor');
    if (!cursor) return;
    
    let mouseX = 0;
    let mouseY = 0;
    let cursorX = 0;
    let cursorY = 0;
    
    // 跟踪鼠标位置
    document.addEventListener('mousemove', (e) => {
        mouseX = e.clientX;
        mouseY = e.clientY;
    });
    
    // 平滑移动光标
    function animateCursor() {
        const speed = 0.1;
        cursorX += (mouseX - cursorX) * speed;
        cursorY += (mouseY - cursorY) * speed;
        
        cursor.style.left = cursorX + 'px';
        cursor.style.top = cursorY + 'px';
        
        requestAnimationFrame(animateCursor);
    }
    
    animateCursor();
    
    // 悬停效果
    const interactiveElements = document.querySelectorAll('a, button, .project-card, .nav-dot, .page-link, .page-link-right');
    interactiveElements.forEach(element => {
        element.addEventListener('mouseenter', () => {
            cursor.classList.add('hover');
        });

        element.addEventListener('mouseleave', () => {
            cursor.classList.remove('hover');
        });

        element.addEventListener('mousedown', () => {
            cursor.classList.add('click');
        });

        element.addEventListener('mouseup', () => {
            cursor.classList.remove('click');
        });
    });
}

// ==================== 加载动画 ====================
function startLoadingAnimation() {
    const loadingOverlay = document.getElementById('loading-overlay');
    const loadingProgressBar = document.getElementById('loading-progress');
    const loadingPercentage = document.getElementById('loading-percentage');
    
    if (!loadingOverlay) return;
    
    // 模拟加载进度
    const loadingInterval = setInterval(() => {
        loadingProgress += Math.random() * 15;
        
        if (loadingProgress >= 100) {
            loadingProgress = 100;
            clearInterval(loadingInterval);
            
            // 完成加载
            setTimeout(() => {
                finishLoading();
            }, 500);
        }
        
        // 更新进度条和百分比
        if (loadingProgressBar) {
            loadingProgressBar.style.width = loadingProgress + '%';
        }
        if (loadingPercentage) {
            loadingPercentage.textContent = Math.round(loadingProgress) + '%';
        }
    }, 100);
}

function finishLoading() {
    const loadingOverlay = document.getElementById('loading-overlay');
    const mainContent = document.getElementById('main-content');
    
    if (loadingOverlay) {
        loadingOverlay.classList.add('fade-out');
        setTimeout(() => {
            loadingOverlay.style.display = 'none';
        }, 500);
    }
    
    if (mainContent) {
        mainContent.style.display = 'block';
        // 添加入场动画
        setTimeout(() => {
            animatePageElements();
        }, 100);
    }
    
    isLoading = false;
}

// ==================== 页面元素动画 ====================
function animatePageElements() {
    // 标题动画
    const titleLines = document.querySelectorAll('.title-line');
    titleLines.forEach((line, index) => {
        line.style.opacity = '0';
        line.style.transform = 'translateY(50px)';
        
        setTimeout(() => {
            line.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
            line.style.opacity = '1';
            line.style.transform = 'translateY(0)';
        }, index * 200);
    });
    
    // 其他元素动画
    const animatedElements = document.querySelectorAll('.hero-subtitle, .hero-cta, .about-description');
    animatedElements.forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            element.style.transition = 'all 0.6s ease-out';
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }, (titleLines.length * 200) + (index * 100));
    });
}

// ==================== 主页功能 ====================
function initHomePage() {
    // 项目卡片悬停效果
    const projectCards = document.querySelectorAll('.project-card');
    projectCards.forEach(card => {
        card.addEventListener('mouseenter', () => {
            card.style.transform = 'translateY(-10px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', () => {
            card.style.transform = 'translateY(0) scale(1)';
        });
    });
    
    // 滚动指示器动画
    const scrollIndicator = document.querySelector('.scroll-indicator');
    if (scrollIndicator) {
        scrollIndicator.addEventListener('click', () => {
            scrollToSection('projects');
        });
    }
}

// ==================== 关于页面功能 ====================
function initAboutPage() {
    // 技能条动画
    const skillBars = document.querySelectorAll('.skill-progress');
    
    // 创建 Intersection Observer 来触发动画
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const progressBar = entry.target;
                const progress = progressBar.getAttribute('data-progress');
                
                setTimeout(() => {
                    progressBar.style.width = progress + '%';
                }, 200);
                
                observer.unobserve(progressBar);
            }
        });
    }, { threshold: 0.5 });
    
    skillBars.forEach(bar => {
        observer.observe(bar);
    });
    
    // 时间线动画
    const timelineItems = document.querySelectorAll('.timeline-item');
    const timelineObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, { threshold: 0.3 });
    
    timelineItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(50px)';
        item.style.transition = `all 0.6s ease-out ${index * 0.1}s`;
        timelineObserver.observe(item);
    });
}

// ==================== 平滑滚动 ====================
function initSmoothScroll() {
    const navLinks = document.querySelectorAll('a[href^="#"]');
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const targetId = link.getAttribute('href').substring(1);
            scrollToSection(targetId);
        });
    });
}

function scrollToSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        const offsetTop = section.offsetTop - 80; // 考虑导航栏高度
        window.scrollTo({
            top: offsetTop,
            behavior: 'smooth'
        });
    }
}

// ==================== 背景文字动画 ====================
function initBackgroundTextAnimation() {
    const backgroundTexts = document.querySelectorAll('.background-text, .background-text-2');
    
    window.addEventListener('scroll', () => {
        const scrollY = window.scrollY;
        
        backgroundTexts.forEach((text, index) => {
            const speed = (index + 1) * 0.5;
            const yPos = -(scrollY * speed);
            text.style.transform = `translateY(${yPos}px) rotate(${index % 2 === 0 ? -15 : 15}deg)`;
        });
    });
}

// ==================== 导航栏滚动效果 ====================
function initNavbarScroll() {
    const navbar = document.querySelector('.navbar');
    if (!navbar) return;
    
    window.addEventListener('scroll', () => {
        if (window.scrollY > 100) {
            navbar.style.background = 'rgba(244, 58, 71, 0.95)';
            navbar.style.backdropFilter = 'blur(20px)';
        } else {
            navbar.style.background = 'rgba(244, 58, 71, 0.9)';
            navbar.style.backdropFilter = 'blur(10px)';
        }
    });
}

// ==================== 页面加载完成后的额外初始化 ====================
window.addEventListener('load', () => {
    initBackgroundTextAnimation();
    initNavbarScroll();
});

// ==================== 工具函数 ====================
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 防抖处理窗口大小变化
window.addEventListener('resize', debounce(() => {
    // 重新计算布局相关的内容
    console.log('Window resized');
}, 250));

// ==================== X OBERON 水印系统 ====================
function initWatermarkSystem() {
    const watermarkTexts = document.querySelectorAll('.watermark-text');

    // 鼠标移动时的水印响应
    function handleWatermarkMouseMove(e) {
        const mouseX = e.clientX / window.innerWidth;
        const mouseY = e.clientY / window.innerHeight;

        watermarkTexts.forEach((text, index) => {
            const intensity = (index + 1) * 0.3;
            const offsetX = (mouseX - 0.5) * 15 * intensity;
            const offsetY = (mouseY - 0.5) * 8 * intensity;

            // 获取当前的transform值并添加鼠标偏移
            const currentTransform = getComputedStyle(text).transform;
            text.style.transform = currentTransform + ` translate(${offsetX}px, ${offsetY}px)`;
        });
    }

    // 事件监听
    document.addEventListener('mousemove', debounce(handleWatermarkMouseMove, 16)); // 60fps
}








// ==================== 滚动驱动动画系统 ====================
function initScrollDrivenAnimations() {
    if (typeof gsap === 'undefined' || typeof ScrollTrigger === 'undefined') {
        console.warn('GSAP or ScrollTrigger not loaded, using fallback animations');
        initFallbackScrollAnimations();
        return;
    }

    // 水印文字滚动视差效果
    const watermarkTexts = document.querySelectorAll('.watermark-text');
    watermarkTexts.forEach((text, index) => {
        gsap.to(text, {
            y: -50 * (index + 1),
            rotation: (index % 2 === 0 ? 3 : -3),
            opacity: 0.12,
            scrollTrigger: {
                trigger: text,
                start: "top bottom",
                end: "bottom top",
                scrub: 1,
            }
        });
    });

    // 主标题滚动效果 - 更强烈的视觉冲击
    const titleLines = document.querySelectorAll('.title-line');
    titleLines.forEach((line, index) => {
        gsap.fromTo(line,
            {
                y: 150,
                opacity: 0,
                scale: 0.7,
                rotationX: 45
            },
            {
                y: 0,
                opacity: 1,
                scale: 1,
                rotationX: 0,
                duration: 1.2,
                delay: index * 0.3,
                ease: "power3.out",
                scrollTrigger: {
                    trigger: line,
                    start: "top 80%",
                    toggleActions: "play none none reverse"
                }
            }
        );
    });

    // 页面切换时的水印变化 - 更戏剧性的效果
    ScrollTrigger.create({
        trigger: ".projects-section",
        start: "top center",
        end: "bottom center",
        onEnter: () => {
            // 进入项目区域时，水印发生戏剧性变化
            gsap.to('.watermark-text', {
                opacity: 0.02,
                scale: 1.1,
                rotation: "+=5",
                duration: 1.2,
                ease: "power2.out",
                stagger: 0.1
            });
        },
        onLeave: () => {
            // 离开项目区域时，恢复水印
            gsap.to('.watermark-text', {
                opacity: 0.08,
                scale: 1,
                rotation: "-=5",
                duration: 1.2,
                ease: "power2.out",
                stagger: 0.1
            });
        },
        onEnterBack: () => {
            gsap.to('.watermark-text', {
                opacity: 0.02,
                scale: 1.1,
                rotation: "+=5",
                duration: 1.2,
                ease: "power2.out",
                stagger: 0.1
            });
        },
        onLeaveBack: () => {
            gsap.to('.watermark-text', {
                opacity: 0.08,
                scale: 1,
                rotation: "-=5",
                duration: 1.2,
                ease: "power2.out",
                stagger: 0.1
            });
        }
    });
}

// ==================== 备用滚动动画 ====================
function initFallbackScrollAnimations() {
    let ticking = false;

    function updateScrollAnimations() {
        const scrollY = window.scrollY;
        const windowHeight = window.innerHeight;

        // 水印视差效果
        const watermarkTexts = document.querySelectorAll('.watermark-text');
        watermarkTexts.forEach((text, index) => {
            const speed = 0.3 + (index * 0.1);
            const yPos = -(scrollY * speed);
            const rotation = scrollY * 0.01 * (index % 2 === 0 ? 1 : -1);

            text.style.transform = `translateY(${yPos}px) rotate(${rotation}deg)`;
        });

        ticking = false;
    }

    function requestTick() {
        if (!ticking) {
            requestAnimationFrame(updateScrollAnimations);
            ticking = true;
        }
    }

    window.addEventListener('scroll', requestTick);
}

// ==================== 页面切换函数 ====================
function scrollToNextPage() {
    const projectsSection = document.getElementById('projects');
    if (projectsSection) {
        projectsSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// ==================== 多页面导航系统 (优化版) ====================
let currentPageIndex = 0;
const totalPages = 5;
let isTransitioning = false;

function initMultiPageNavigation() {
    const navDots = document.querySelectorAll('.nav-dot');
    const scrollContainer = document.getElementById('scroll-container');

    // 导航点点击事件
    navDots.forEach((dot, index) => {
        dot.addEventListener('click', () => {
            if (!isTransitioning) {
                navigateToPage(index);
            }
        });
    });

    // 滚动监听 - 使用防抖优化
    if (scrollContainer) {
        scrollContainer.addEventListener('scroll', debounce(handlePageScroll, 50));
    }

    // 键盘导航
    document.addEventListener('keydown', (e) => {
        if (isTransitioning) return;

        switch(e.key) {
            case 'ArrowDown':
            case ' ':
                e.preventDefault();
                if (currentPageIndex < totalPages - 1) {
                    navigateToPage(currentPageIndex + 1);
                }
                break;
            case 'ArrowUp':
                e.preventDefault();
                if (currentPageIndex > 0) {
                    navigateToPage(currentPageIndex - 1);
                }
                break;
        }
    });

    // 鼠标滚轮导航 - 优化版
    let wheelTimeout;
    let wheelDelta = 0;

    scrollContainer.addEventListener('wheel', (e) => {
        e.preventDefault(); // 阻止默认滚动

        if (isTransitioning) return;

        wheelDelta += e.deltaY;

        clearTimeout(wheelTimeout);
        wheelTimeout = setTimeout(() => {
            if (Math.abs(wheelDelta) > 50) { // 滚动阈值
                if (wheelDelta > 0 && currentPageIndex < totalPages - 1) {
                    navigateToPage(currentPageIndex + 1);
                } else if (wheelDelta < 0 && currentPageIndex > 0) {
                    navigateToPage(currentPageIndex - 1);
                }
            }
            wheelDelta = 0;
        }, 50);
    }, { passive: false });

    // 初始化第一页
    updateNavigationDots(0);
    triggerPageAnimations(0);
}

function navigateToPage(pageIndex) {
    if (pageIndex < 0 || pageIndex >= totalPages || isTransitioning) return;

    isTransitioning = true;
    const scrollContainer = document.getElementById('scroll-container');
    const targetPage = document.getElementById(`page${pageIndex + 1}`);
    const allPages = document.querySelectorAll('.page-section');

    if (targetPage && scrollContainer) {
        // 立即更新页面状态
        allPages.forEach(page => page.classList.remove('active-section'));
        targetPage.classList.add('active-section');

        currentPageIndex = pageIndex;
        updateNavigationDots(pageIndex);

        // 平滑滚动到目标页面
        scrollContainer.scrollTo({
            top: targetPage.offsetTop,
            behavior: 'smooth'
        });

        // 触发页面动画
        requestAnimationFrame(() => {
            triggerPageAnimations(pageIndex);
        });

        // 较短的过渡时间
        setTimeout(() => {
            isTransitioning = false;
        }, 500);
    } else {
        isTransitioning = false;
    }
}

function handlePageScroll() {
    if (isTransitioning) return;

    const scrollContainer = document.getElementById('scroll-container');
    const sections = document.querySelectorAll('.page-section');

    if (!scrollContainer || !sections.length) return;

    const scrollPosition = scrollContainer.scrollTop;
    const windowHeight = window.innerHeight;

    // 简化的页面检测 - 基于滚动位置
    const newPageIndex = Math.round(scrollPosition / windowHeight);
    const clampedPageIndex = Math.max(0, Math.min(newPageIndex, totalPages - 1));



    // 只有当页面真正改变时才更新
    if (clampedPageIndex !== currentPageIndex) {
        // 移除所有active状态
        sections.forEach(s => s.classList.remove('active-section'));

        // 添加新的active状态
        if (sections[clampedPageIndex]) {
            sections[clampedPageIndex].classList.add('active-section');
        }

        currentPageIndex = clampedPageIndex;
        updateNavigationDots(clampedPageIndex);

        // 使用requestAnimationFrame优化动画触发
        requestAnimationFrame(() => {
            triggerPageAnimations(clampedPageIndex);
        });
    }

    // 更新滚动提示的显示
    const scrollHint = document.querySelector('.scroll-hint');
    if (scrollHint) {
        scrollHint.style.opacity = currentPageIndex === totalPages - 1 ? '0' : '0.7';
    }
}

function updateNavigationDots(activeIndex) {
    const navDots = document.querySelectorAll('.nav-dot');
    navDots.forEach((dot, index) => {
        if (index === activeIndex) {
            dot.classList.add('active');
        } else {
            dot.classList.remove('active');
        }
    });
}

function triggerPageAnimations(pageIndex) {
    // 重置所有页面的动画状态
    resetPageAnimations();

    switch(pageIndex) {
        case 0:
            animateHomePage();
            break;
        case 1:
            animateAboutPage();
            break;
        case 2:
            animateIdentityPage();
            break;
        case 3:
            animateCreatorPage();
            break;
        case 4:
            animateDeveloperPage();
            break;
    }
}

function resetPageAnimations() {
    // 清理水波Three.js资源
    if (isWaterActive) {
        cleanupWater();
    }

    // 重置所有可能的动画元素
    const animatedElements = document.querySelectorAll(
        '.title-line, .hero-subtitle, .hero-cta, .about-title, .about-description, .identity-title, .identity-subtitle, .whoami-title, .whoami-title-right'
    );

    animatedElements.forEach(el => {
        el.style.transition = 'none';
        el.style.transform = '';
        el.style.opacity = '';
        el.classList.remove('animate');
    });
}

function animateHomePage() {
    const titleLines = document.querySelectorAll('.title-line');
    const subtitle = document.querySelector('.hero-subtitle');
    const cta = document.querySelector('.hero-cta');

    titleLines.forEach((line, index) => {
        setTimeout(() => {
            line.style.transition = 'all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
            line.style.transform = 'translate3d(0, 0, 0)';
            line.style.opacity = '1';
        }, index * 150);
    });

    if (subtitle) {
        setTimeout(() => {
            subtitle.style.transition = 'all 0.5s ease-out';
            subtitle.style.transform = 'translate3d(0, 0, 0)';
            subtitle.style.opacity = '1';
        }, 450);
    }

    if (cta) {
        setTimeout(() => {
            cta.style.transition = 'all 0.5s ease-out';
            cta.style.transform = 'translate3d(0, 0, 0)';
            cta.style.opacity = '1';
        }, 600);
    }
}

function animateAboutPage() {
    const title = document.querySelector('.about-title');
    const lines = document.querySelectorAll('.about-line, .about-paragraph');

    if (title) {
        setTimeout(() => {
            title.style.transition = 'all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
            title.style.transform = 'translate3d(0, 0, 0)';
            title.style.opacity = '1';
        }, 100);
    }

    lines.forEach((el, index) => {
        if (el) {
            setTimeout(() => {
                el.style.transition = 'all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
                el.style.transform = 'translate3d(0, 0, 0)';
                el.style.opacity = '1';
            }, 250 + (index * 150));
        }
    });
}

// ==================== 第3页 - Three.js 3D场景 ====================
let threeScene, threeCamera, threeRenderer;
let threeAnimationId;
let currentScene = 'geometric';
let isThreeJSActive = false;
let sceneObjects = [];
let mouseX = 0, mouseY = 0;
let targetRotationX = 0, targetRotationY = 0;

// ==================== 第3页 - 水波游泳池效果 ====================
let waterScene, waterCamera, waterRenderer;
let waterAnimationId;
let isWaterActive = false;
let waterGeometry, waterMaterial, waterMesh;
let mouse = new THREE.Vector2();
let raycaster = new THREE.Raycaster();

function animateIdentityPage() {
    if (isWaterActive) return;
    isWaterActive = true;

    console.log('启动水波游泳池效果...');

    // 初始化水波Three.js
    initWaterThreeJS();

    // 延迟显示内容
    setTimeout(() => {
        hideLoadingIndicator();
        showWaterContent();
    }, 1500);
}

function initWaterThreeJS() {
    console.log('初始化水波Three.js场景...');

    const canvas = document.getElementById('water-canvas');
    const container = document.getElementById('water-container');

    if (!canvas || !container) {
        console.error('找不到水波canvas或container元素');
        return;
    }

    // 创建场景 - 使用主体配色
    waterScene = new THREE.Scene();
    waterScene.background = new THREE.Color(0xf43a47);

    // 创建相机
    waterCamera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    waterCamera.position.set(0, 5, 10);
    waterCamera.lookAt(0, 0, 0);

    // 创建渲染器
    waterRenderer = new THREE.WebGLRenderer({
        canvas: canvas,
        antialias: true
    });
    waterRenderer.setSize(window.innerWidth, window.innerHeight);
    waterRenderer.shadowMap.enabled = true;
    waterRenderer.shadowMap.type = THREE.PCFSoftShadowMap;

    // 添加光源
    addWaterLights();

    // 创建水波效果
    createWaterSurface();

    // 添加鼠标交互
    setupWaterInteraction(canvas);

    // 开始渲染循环
    animateWater();

    console.log('水波Three.js场景初始化完成！');
}

function addWaterLights() {
    // 由于使用MeshBasicMaterial，光照不会影响颜色
    // 但保留一些光源用于场景氛围
    const ambientLight = new THREE.AmbientLight(0xffffff, 1.0);
    waterScene.add(ambientLight);
}

function createWaterSurface() {
    // 创建水面几何体 - 高分辨率平面
    const width = 20;
    const height = 20;
    const widthSegments = 128;
    const heightSegments = 128;

    waterGeometry = new THREE.PlaneGeometry(width, height, widthSegments, heightSegments);
    waterGeometry.rotateX(-Math.PI / 2); // 水平放置

    // 创建水波材质 - 纯白色
    waterMaterial = new THREE.MeshBasicMaterial({
        color: 0xffffff,
        transparent: true,
        opacity: 0.9
    });

    // 创建水面网格
    waterMesh = new THREE.Mesh(waterGeometry, waterMaterial);
    waterMesh.receiveShadow = true;
    waterScene.add(waterMesh);

    // 存储原始顶点位置
    const positions = waterGeometry.attributes.position.array;
    waterGeometry.userData.originalPositions = new Float32Array(positions);
    waterGeometry.userData.waves = [];
}

function setupWaterInteraction(canvas) {
    // 鼠标移动事件
    canvas.addEventListener('mousemove', (event) => {
        const rect = canvas.getBoundingClientRect();
        mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
    });

    // 鼠标点击事件 - 创建水波
    canvas.addEventListener('click', (event) => {
        const rect = canvas.getBoundingClientRect();
        mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

        // 射线检测
        raycaster.setFromCamera(mouse, waterCamera);
        const intersects = raycaster.intersectObject(waterMesh);

        if (intersects.length > 0) {
            const point = intersects[0].point;
            createWaveAt(point.x, point.z);
        }
    });
}

function createWaveAt(x, z) {
    // 添加新的波浪
    waterGeometry.userData.waves.push({
        centerX: x,
        centerZ: z,
        radius: 0,
        amplitude: 0.5,
        speed: 2,
        decay: 0.98
    });
}

function updateWaterWaves() {
    const positions = waterGeometry.attributes.position.array;
    const originalPositions = waterGeometry.userData.originalPositions;
    const waves = waterGeometry.userData.waves;

    // 重置到原始位置
    for (let i = 0; i < positions.length; i++) {
        positions[i] = originalPositions[i];
    }

    // 应用所有波浪
    for (let w = waves.length - 1; w >= 0; w--) {
        const wave = waves[w];

        for (let i = 0; i < positions.length; i += 3) {
            const x = positions[i];
            const z = positions[i + 2];

            const distance = Math.sqrt((x - wave.centerX) ** 2 + (z - wave.centerZ) ** 2);

            if (distance < wave.radius) {
                const ripple = Math.sin((wave.radius - distance) * 0.5) * wave.amplitude;
                positions[i + 1] += ripple;
            }
        }

        // 更新波浪参数
        wave.radius += wave.speed * 0.1;
        wave.amplitude *= wave.decay;

        // 移除衰减的波浪
        if (wave.amplitude < 0.01) {
            waves.splice(w, 1);
        }
    }

    // 添加持续的背景波浪
    const time = Date.now() * 0.001;
    for (let i = 0; i < positions.length; i += 3) {
        const x = positions[i];
        const z = positions[i + 2];

        const wave1 = Math.sin(x * 0.3 + time * 2) * 0.1;
        const wave2 = Math.sin(z * 0.2 + time * 1.5) * 0.08;
        const wave3 = Math.sin((x + z) * 0.1 + time * 3) * 0.05;

        positions[i + 1] += wave1 + wave2 + wave3;
    }

    waterGeometry.attributes.position.needsUpdate = true;
    waterGeometry.computeVertexNormals();
}

function animateWater() {
    if (!isWaterActive) return;

    waterAnimationId = requestAnimationFrame(animateWater);

    // 更新水波
    updateWaterWaves();

    // 相机固定位置 - 保持红白分界线稳定
    waterCamera.position.set(0, 5, 10);
    waterCamera.lookAt(0, 0, 0);

    // 渲染场景
    waterRenderer.render(waterScene, waterCamera);
}



function showWaterContent() {
    const identityContent = document.getElementById('identity-content');
    const titles = document.querySelectorAll('.identity-title');
    const subtitle = document.querySelector('.identity-subtitle');

    // 显示主要内容
    if (identityContent) {
        identityContent.classList.add('show');
    }

    // 标题动画
    titles.forEach((title, index) => {
        setTimeout(() => {
            title.classList.add('animate');
        }, 500 + index * 300);
    });

    // 副标题动画
    if (subtitle) {
        setTimeout(() => {
            subtitle.classList.add('animate');
        }, 1400);
    }

    console.log('水波内容动画已启动！');
}

// 清理水波资源
function cleanupWater() {
    isWaterActive = false;

    if (waterAnimationId) {
        cancelAnimationFrame(waterAnimationId);
        waterAnimationId = null;
    }

    if (waterRenderer) {
        waterRenderer.dispose();
        waterRenderer = null;
    }

    if (waterGeometry) {
        waterGeometry.dispose();
        waterGeometry = null;
    }

    if (waterMaterial) {
        waterMaterial.dispose();
        waterMaterial = null;
    }

    waterScene = null;
    waterCamera = null;
    waterMesh = null;

    console.log('水波资源已清理');
}

function initThreeJS() {
    console.log('开始初始化Three.js...');

    // 检查Three.js是否加载
    if (typeof THREE === 'undefined') {
        console.error('Three.js未加载！');
        const loadingText = document.querySelector('.loading-text');
        if (loadingText) {
            loadingText.textContent = 'Three.js加载失败！';
        }
        return;
    }

    const canvas = document.getElementById('threejs-canvas');
    const container = document.getElementById('threejs-container');

    if (!canvas || !container) {
        console.error('找不到canvas或container元素');
        return;
    }

    console.log('Three.js加载成功，开始创建场景...');

    // 创建场景
    threeScene = new THREE.Scene();

    // 创建相机
    threeCamera = new THREE.PerspectiveCamera(
        75,
        window.innerWidth / window.innerHeight,
        0.1,
        1000
    );
    threeCamera.position.z = 5;

    // 创建渲染器
    threeRenderer = new THREE.WebGLRenderer({
        canvas: canvas,
        alpha: true,
        antialias: true
    });
    threeRenderer.setSize(window.innerWidth, window.innerHeight);
    threeRenderer.setClearColor(0x000000, 0);
    threeRenderer.shadowMap.enabled = true;
    threeRenderer.shadowMap.type = THREE.PCFSoftShadowMap;

    // 简单的鼠标控制（不依赖OrbitControls）
    canvas.addEventListener('mousemove', (event) => {
        mouseX = (event.clientX - window.innerWidth / 2) * 0.001;
        mouseY = (event.clientY - window.innerHeight / 2) * 0.001;
        targetRotationX = mouseY;
        targetRotationY = mouseX;
    });

    // 添加光源
    addLights();

    // 创建简洁3D场景（第三页固定场景）
    createSimpleScene();

    // 开始渲染循环
    animate();

    console.log('Three.js初始化完成！');
}

function addLights() {
    // 环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    threeScene.add(ambientLight);

    // 方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    threeScene.add(directionalLight);

    // 点光源
    const pointLight = new THREE.PointLight(0xff0000, 0.5, 100);
    pointLight.position.set(-10, -10, -10);
    threeScene.add(pointLight);
}

function createGeometricScene() {
    clearScene();

    // 创建几何体组合
    const group = new THREE.Group();

    // 中心立方体
    const cubeGeometry = new THREE.BoxGeometry(1, 1, 1);
    const cubeMaterial = new THREE.MeshPhongMaterial({
        color: 0x000000,
        transparent: true,
        opacity: 0.8,
        wireframe: false
    });
    const cube = new THREE.Mesh(cubeGeometry, cubeMaterial);
    cube.castShadow = true;
    cube.receiveShadow = true;
    group.add(cube);

    // 围绕的球体
    for (let i = 0; i < 8; i++) {
        const sphereGeometry = new THREE.SphereGeometry(0.2, 16, 16);
        const sphereMaterial = new THREE.MeshPhongMaterial({
            color: new THREE.Color().setHSL(i / 8, 0.7, 0.5),
            transparent: true,
            opacity: 0.9
        });
        const sphere = new THREE.Mesh(sphereGeometry, sphereMaterial);

        const angle = (i / 8) * Math.PI * 2;
        sphere.position.x = Math.cos(angle) * 2;
        sphere.position.y = Math.sin(angle) * 2;
        sphere.position.z = Math.sin(angle * 2) * 0.5;

        sphere.castShadow = true;
        group.add(sphere);
    }

    // 添加线框
    const wireframeGeometry = new THREE.IcosahedronGeometry(3, 1);
    const wireframeMaterial = new THREE.MeshBasicMaterial({
        color: 0x000000,
        wireframe: true,
        transparent: true,
        opacity: 0.3
    });
    const wireframe = new THREE.Mesh(wireframeGeometry, wireframeMaterial);
    group.add(wireframe);

    threeScene.add(group);
    sceneObjects.push(group);
}

function createSimpleScene() {
    clearScene();

    // 创建简洁的几何体组合 - 使用主体配色
    const group = new THREE.Group();

    // 1. 中心红色立方体
    const centerGeometry = new THREE.BoxGeometry(2, 2, 2);
    const centerMaterial = new THREE.MeshPhongMaterial({
        color: 0xf43a47,
        transparent: true,
        opacity: 0.9
    });
    const centerCube = new THREE.Mesh(centerGeometry, centerMaterial);
    centerCube.castShadow = true;
    centerCube.receiveShadow = true;
    group.add(centerCube);

    // 2. 围绕的黑色线框立方体
    for (let i = 0; i < 4; i++) {
        const wireGeometry = new THREE.BoxGeometry(1, 1, 1);
        const wireMaterial = new THREE.MeshBasicMaterial({
            color: 0x000000,
            wireframe: true,
            transparent: true,
            opacity: 0.6
        });
        const wireCube = new THREE.Mesh(wireGeometry, wireMaterial);

        const angle = (i / 4) * Math.PI * 2;
        wireCube.position.x = Math.cos(angle) * 4;
        wireCube.position.z = Math.sin(angle) * 4;
        wireCube.position.y = Math.sin(angle * 2) * 1;

        group.add(wireCube);
    }

    // 3. 简洁的粒子效果 - 红色主题
    const particleCount = 200;
    const particleGeometry = new THREE.BufferGeometry();
    const positions = new Float32Array(particleCount * 3);
    const colors = new Float32Array(particleCount * 3);

    for (let i = 0; i < particleCount; i++) {
        const i3 = i * 3;

        // 创建简洁的圆形分布
        const radius = 8 + Math.random() * 4;
        const angle = (i / particleCount) * Math.PI * 4;
        const height = (Math.random() - 0.5) * 6;

        positions[i3] = Math.cos(angle) * radius;
        positions[i3 + 1] = height;
        positions[i3 + 2] = Math.sin(angle) * radius;

        // 红色主题的颜色
        const color = new THREE.Color();
        const redIntensity = 0.8 + Math.random() * 0.2;
        color.setRGB(redIntensity, 0.2, 0.2);
        colors[i3] = color.r;
        colors[i3 + 1] = color.g;
        colors[i3 + 2] = color.b;
    }

    particleGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    particleGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

    const particleMaterial = new THREE.PointsMaterial({
        size: 0.08,
        vertexColors: true,
        transparent: true,
        opacity: 0.7
    });

    const particles = new THREE.Points(particleGeometry, particleMaterial);
    group.add(particles);

    threeScene.add(group);
    sceneObjects.push(group);
}

function createMorphingScene() {
    clearScene();

    // 创建变形几何体
    const geometry1 = new THREE.BoxGeometry(2, 2, 2);
    const geometry2 = new THREE.SphereGeometry(1.5, 32, 32);

    // 创建变形材质
    const material = new THREE.MeshPhongMaterial({
        color: 0x000000,
        transparent: true,
        opacity: 0.8,
        wireframe: false
    });

    const mesh = new THREE.Mesh(geometry1, material);
    mesh.castShadow = true;
    mesh.receiveShadow = true;

    // 添加变形动画
    mesh.userData = {
        morphing: true,
        originalGeometry: geometry1,
        targetGeometry: geometry2,
        morphProgress: 0
    };

    threeScene.add(mesh);
    sceneObjects.push(mesh);
}

function createGalaxyScene() {
    clearScene();

    // 创建星系螺旋
    const group = new THREE.Group();
    const particleCount = 2000;
    const geometry = new THREE.BufferGeometry();
    const positions = new Float32Array(particleCount * 3);
    const colors = new Float32Array(particleCount * 3);

    for (let i = 0; i < particleCount; i++) {
        const i3 = i * 3;

        // 螺旋参数
        const radius = (i / particleCount) * 15;
        const angle = (i / particleCount) * Math.PI * 8;
        const height = (Math.random() - 0.5) * 2;

        positions[i3] = Math.cos(angle) * radius;
        positions[i3 + 1] = height;
        positions[i3 + 2] = Math.sin(angle) * radius;

        // 颜色渐变
        const color = new THREE.Color();
        const hue = (i / particleCount) * 0.3 + 0.6; // 蓝紫色调
        color.setHSL(hue, 0.8, 0.7);
        colors[i3] = color.r;
        colors[i3 + 1] = color.g;
        colors[i3 + 2] = color.b;
    }

    geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

    const material = new THREE.PointsMaterial({
        size: 0.05,
        vertexColors: true,
        transparent: true,
        opacity: 0.9
    });

    const galaxy = new THREE.Points(geometry, material);
    group.add(galaxy);

    // 添加中心黑洞效果
    const blackHoleGeometry = new THREE.SphereGeometry(0.5, 32, 32);
    const blackHoleMaterial = new THREE.MeshBasicMaterial({
        color: 0x000000,
        transparent: true,
        opacity: 0.9
    });
    const blackHole = new THREE.Mesh(blackHoleGeometry, blackHoleMaterial);
    group.add(blackHole);

    threeScene.add(group);
    sceneObjects.push(group);
}

function clearScene() {
    sceneObjects.forEach(obj => {
        threeScene.remove(obj);
        if (obj.geometry) obj.geometry.dispose();
        if (obj.material) {
            if (Array.isArray(obj.material)) {
                obj.material.forEach(mat => mat.dispose());
            } else {
                obj.material.dispose();
            }
        }
    });
    sceneObjects = [];
}

function animate() {
    if (!isThreeJSActive) return;

    threeAnimationId = requestAnimationFrame(animate);

    const time = Date.now() * 0.001;

    // 简单的相机旋转控制
    if (threeCamera) {
        threeCamera.position.x += (targetRotationX - threeCamera.rotation.x) * 0.05;
        threeCamera.position.y += (targetRotationY - threeCamera.rotation.y) * 0.05;
        threeCamera.lookAt(0, 0, 0);
    }

    // 简洁3D场景动画
    sceneObjects.forEach(obj => {
        // 整体缓慢旋转
        obj.rotation.y = time * 0.05;
        obj.rotation.x = Math.sin(time * 0.3) * 0.05;

        // 子对象动画
        if (obj.children) {
            obj.children.forEach((child, index) => {
                if (index === 0) {
                    // 中心立方体 - 轻微缩放和旋转
                    child.rotation.x = time * 0.2;
                    child.rotation.y = time * 0.15;
                    child.scale.setScalar(1 + Math.sin(time * 1.5) * 0.1);
                } else if (index <= 4) {
                    // 围绕的线框立方体 - 轨道运动
                    const orbitSpeed = time * 0.3;
                    const radius = 4;
                    const angle = (index / 4) * Math.PI * 2 + orbitSpeed;
                    child.position.x = Math.cos(angle) * radius;
                    child.position.z = Math.sin(angle) * radius;
                    child.position.y = Math.sin(angle * 2 + time) * 1;
                    child.rotation.x = time * 0.4;
                    child.rotation.y = time * 0.3;
                } else {
                    // 粒子系统 - 轻微旋转
                    child.rotation.y = time * 0.08;
                }
            });
        }
    });

    // 渲染场景
    threeRenderer.render(threeScene, threeCamera);
}

function setupSceneControls() {
    const sceneSelector = document.getElementById('scene-selector');
    const playPauseBtn = document.getElementById('play-pause-btn');
    const resetBtn = document.getElementById('reset-btn');

    if (sceneSelector) {
        sceneSelector.addEventListener('change', (e) => {
            currentScene = e.target.value;
            switchScene(currentScene);
        });
    }

    if (playPauseBtn) {
        playPauseBtn.addEventListener('click', () => {
            if (isThreeJSActive) {
                pauseAnimation();
                playPauseBtn.textContent = '播放';
            } else {
                resumeAnimation();
                playPauseBtn.textContent = '暂停';
            }
        });
    }

    if (resetBtn) {
        resetBtn.addEventListener('click', () => {
            resetCamera();
        });
    }
}

function switchScene(sceneName) {
    switch(sceneName) {
        case 'geometric':
            createGeometricScene();
            break;
        case 'particles':
            createParticlesScene();
            break;
        case 'morphing':
            createMorphingScene();
            break;
        case 'galaxy':
            createGalaxyScene();
            break;
    }
}

function pauseAnimation() {
    isThreeJSActive = false;
    if (threeAnimationId) {
        cancelAnimationFrame(threeAnimationId);
    }
}

function resumeAnimation() {
    isThreeJSActive = true;
    animate();
}

function resetCamera() {
    if (threeCamera) {
        threeCamera.position.set(0, 0, 5);
        threeCamera.rotation.set(0, 0, 0);
        targetRotationX = 0;
        targetRotationY = 0;
    }
}

function showLoadingIndicator() {
    const loadingIndicator = document.getElementById('loading-indicator');
    if (loadingIndicator) {
        loadingIndicator.classList.remove('hide');
    }
}

function hideLoadingIndicator() {
    const loadingIndicator = document.getElementById('loading-indicator');
    if (loadingIndicator) {
        loadingIndicator.classList.add('hide');
    }
}

function showIdentityContent() {
    const identityContent = document.getElementById('identity-content');

    // 获取简洁的文字元素
    const mainTitle = document.querySelector('.identity-main-title');
    const subtitle = document.querySelector('.identity-subtitle');
    const description = document.querySelector('.identity-description');

    // 显示主要内容
    if (identityContent) {
        identityContent.classList.add('show');
    }

    // 主标题动画 - 立即开始
    if (mainTitle) {
        setTimeout(() => {
            mainTitle.classList.add('animate');
        }, 200);
    }

    // 副标题动画
    if (subtitle) {
        setTimeout(() => {
            subtitle.classList.add('animate');
        }, 500);
    }

    // 描述文字动画
    if (description) {
        setTimeout(() => {
            description.classList.add('animate');
        }, 800);
    }

    console.log('简洁身份展示动画已启动！');
}

// 清理函数
function cleanupThreeJS() {
    isThreeJSActive = false;

    if (threeAnimationId) {
        cancelAnimationFrame(threeAnimationId);
        threeAnimationId = null;
    }

    if (threeRenderer) {
        threeRenderer.dispose();
        threeRenderer = null;
    }

    if (threeScene) {
        clearScene();
        threeScene = null;
    }

    // 重置鼠标控制变量
    targetRotationX = 0;
    targetRotationY = 0;

    threeCamera = null;

    // 重置UI状态
    const identityContent = document.getElementById('identity-content');
    const sceneDescription = document.getElementById('scene-description');
    const loadingIndicator = document.getElementById('loading-indicator');
    const titles = document.querySelectorAll('.identity-title');

    if (identityContent) {
        identityContent.classList.remove('show');
    }

    if (sceneDescription) {
        sceneDescription.classList.remove('show');
    }

    if (loadingIndicator) {
        loadingIndicator.classList.remove('hide');
    }

    titles.forEach(title => {
        title.classList.remove('animate');
    });
}

// ==================== 第4页 - 创作者 (Three.js过渡版本) ====================
let creativeScene, creativeCamera, creativeRenderer;
let creativeAnimationId;
let isCreativePageActive = false;
let creativeObjects = [];
let creativeMouseX = 0, creativeMouseY = 0;

function animateCreatorPage() {
    if (isCreativePageActive) return;
    isCreativePageActive = true;

    console.log('开始第四页Three.js过渡动画...');

    // 显示过渡指示器
    showTransitionIndicator();

    // 初始化创意场景
    initCreativeThreeJS();

    // 延迟显示内容
    setTimeout(() => {
        hideTransitionIndicator();
        showCreativeContent();
    }, 1500);
}

function initCreativeThreeJS() {
    console.log('初始化创意Three.js场景...');

    const canvas = document.getElementById('creative-canvas');
    const container = document.getElementById('creative-container');

    if (!canvas || !container) {
        console.error('找不到创意canvas或container元素');
        return;
    }

    // 创建场景
    creativeScene = new THREE.Scene();

    // 创建相机
    creativeCamera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    creativeCamera.position.z = 8;

    // 创建渲染器
    creativeRenderer = new THREE.WebGLRenderer({
        canvas: canvas,
        alpha: true,
        antialias: true
    });
    creativeRenderer.setSize(window.innerWidth, window.innerHeight);
    creativeRenderer.setClearColor(0x000000, 0);

    // 添加光源
    addCreativeLights();

    // 创建创意场景（从粒子过渡到几何体）
    createCreativeTransitionScene();

    // 鼠标控制
    canvas.addEventListener('mousemove', (event) => {
        creativeMouseX = (event.clientX - window.innerWidth / 2) * 0.001;
        creativeMouseY = (event.clientY - window.innerHeight / 2) * 0.001;
    });

    // 开始渲染循环
    animateCreativeThreeJS();

    console.log('创意Three.js场景初始化完成！');
}

function addCreativeLights() {
    // 环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
    creativeScene.add(ambientLight);

    // 方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.6);
    directionalLight.position.set(5, 5, 5);
    creativeScene.add(directionalLight);

    // 彩色点光源
    const pointLight1 = new THREE.PointLight(0xff6b6b, 0.8, 50);
    pointLight1.position.set(-10, 10, 10);
    creativeScene.add(pointLight1);

    const pointLight2 = new THREE.PointLight(0x4ecdc4, 0.8, 50);
    pointLight2.position.set(10, -10, 10);
    creativeScene.add(pointLight2);
}

function createCreativeTransitionScene() {
    clearCreativeScene();

    // 创建从粒子过渡到几何体的混合场景
    const group = new THREE.Group();

    // 1. 保留一些粒子作为过渡
    const particleCount = 200;
    const particleGeometry = new THREE.BufferGeometry();
    const positions = new Float32Array(particleCount * 3);
    const colors = new Float32Array(particleCount * 3);

    for (let i = 0; i < particleCount; i++) {
        const i3 = i * 3;

        // 创建螺旋分布
        const radius = (i / particleCount) * 8;
        const angle = (i / particleCount) * Math.PI * 6;

        positions[i3] = Math.cos(angle) * radius;
        positions[i3 + 1] = (Math.random() - 0.5) * 10;
        positions[i3 + 2] = Math.sin(angle) * radius;

        // 暖色调
        const color = new THREE.Color();
        color.setHSL(0.1 + Math.random() * 0.2, 0.8, 0.6);
        colors[i3] = color.r;
        colors[i3 + 1] = color.g;
        colors[i3 + 2] = color.b;
    }

    particleGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    particleGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

    const particleMaterial = new THREE.PointsMaterial({
        size: 0.15,
        vertexColors: true,
        transparent: true,
        opacity: 0.8
    });

    const particles = new THREE.Points(particleGeometry, particleMaterial);
    group.add(particles);

    // 2. 添加创意几何体
    const creativeShapes = [];

    // 中心创意球体
    const sphereGeometry = new THREE.IcosahedronGeometry(2, 1);
    const sphereMaterial = new THREE.MeshPhongMaterial({
        color: 0x000000,
        transparent: true,
        opacity: 0.7,
        wireframe: true
    });
    const sphere = new THREE.Mesh(sphereGeometry, sphereMaterial);
    creativeShapes.push(sphere);
    group.add(sphere);

    // 围绕的创意立方体
    for (let i = 0; i < 6; i++) {
        const cubeGeometry = new THREE.BoxGeometry(0.8, 0.8, 0.8);
        const cubeMaterial = new THREE.MeshPhongMaterial({
            color: new THREE.Color().setHSL(i / 6, 0.7, 0.5),
            transparent: true,
            opacity: 0.8
        });
        const cube = new THREE.Mesh(cubeGeometry, cubeMaterial);

        const angle = (i / 6) * Math.PI * 2;
        cube.position.x = Math.cos(angle) * 4;
        cube.position.y = Math.sin(angle * 2) * 2;
        cube.position.z = Math.sin(angle) * 4;

        creativeShapes.push(cube);
        group.add(cube);
    }

    // 存储对象引用
    group.userData = { particles, creativeShapes };

    creativeScene.add(group);
    creativeObjects.push(group);
}

function clearCreativeScene() {
    creativeObjects.forEach(obj => {
        creativeScene.remove(obj);
        if (obj.geometry) obj.geometry.dispose();
        if (obj.material) {
            if (Array.isArray(obj.material)) {
                obj.material.forEach(mat => mat.dispose());
            } else {
                obj.material.dispose();
            }
        }
    });
    creativeObjects = [];
}

function animateCreativeThreeJS() {
    if (!isCreativePageActive) return;

    creativeAnimationId = requestAnimationFrame(animateCreativeThreeJS);

    const time = Date.now() * 0.001;

    // 相机跟随鼠标
    if (creativeCamera) {
        creativeCamera.position.x += (creativeMouseX * 5 - creativeCamera.position.x) * 0.05;
        creativeCamera.position.y += (-creativeMouseY * 5 - creativeCamera.position.y) * 0.05;
        creativeCamera.lookAt(0, 0, 0);
    }

    // 创意场景动画
    creativeObjects.forEach(obj => {
        if (obj.userData) {
            // 粒子旋转
            if (obj.userData.particles) {
                obj.userData.particles.rotation.y = time * 0.2;
            }

            // 几何体动画
            if (obj.userData.creativeShapes) {
                obj.userData.creativeShapes.forEach((shape, index) => {
                    if (index === 0) {
                        // 中心球体
                        shape.rotation.x = time * 0.3;
                        shape.rotation.y = time * 0.2;
                        shape.scale.setScalar(1 + Math.sin(time * 2) * 0.1);
                    } else {
                        // 围绕立方体
                        shape.rotation.x = time * 0.4;
                        shape.rotation.y = time * 0.3;

                        // 轨道运动
                        const angle = (index / 6) * Math.PI * 2 + time * 0.5;
                        shape.position.x = Math.cos(angle) * 4;
                        shape.position.z = Math.sin(angle) * 4;
                        shape.position.y = Math.sin(angle * 2 + time) * 2;
                    }
                });
            }
        }
    });

    // 渲染场景
    creativeRenderer.render(creativeScene, creativeCamera);
}

function showTransitionIndicator() {
    const transitionIndicator = document.getElementById('transition-indicator');
    if (transitionIndicator) {
        transitionIndicator.classList.add('show');
    }
}

function hideTransitionIndicator() {
    const transitionIndicator = document.getElementById('transition-indicator');
    if (transitionIndicator) {
        transitionIndicator.classList.remove('show');
    }
}

function showCreativeContent() {
    const creativeContent = document.getElementById('creative-content');
    const creativePhilosophy = document.getElementById('creative-philosophy');
    const titles = document.querySelectorAll('.creative-title');
    const philosophyLines = document.querySelectorAll('.creative-philosophy .philosophy-line');

    // 显示主要内容
    if (creativeContent) {
        creativeContent.classList.add('show');
    }

    // 标题动画
    titles.forEach((title, index) => {
        setTimeout(() => {
            title.classList.add('animate');
        }, 500 + index * 300);
    });

    // 设计理念动画
    if (creativePhilosophy) {
        setTimeout(() => {
            creativePhilosophy.classList.add('show');
        }, 1500);

        philosophyLines.forEach((line, index) => {
            setTimeout(() => {
                line.classList.add('animate');
            }, 2000 + index * 200);
        });
    }
}

function animateDeveloperPage() {
    // 初始化堆叠卡片交互 - 完全按照您的React代码
    initStackedCards();
}

// 现代化堆叠卡片交互功能
function initStackedCards() {
    const stackedCards = document.querySelectorAll('.stacked-card');
    const detailPlaceholder = document.getElementById('detail-placeholder');
    const detailContent = document.getElementById('detail-content');
    let hoveredCard = null;

    // 卡片数据
    const cardData = {
        'walkman': {
            id: 'walkman',
            number: '010',
            title: 'Walkman - 1979',
            subtitle: 'vintage audio',
            image: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=256&h=192&fit=crop&crop=center',
            category: 'S',
            description: 'A vintage Sony Walkman from 1979, representing the revolutionary portable music experience that changed how people consumed audio content. This iconic device made personal music listening truly mobile for the first time.',
            details: ['Year: 1979', 'Brand: Sony', 'Type: Portable Cassette Player', 'Condition: Vintage']
        },
        'oil-lamp': {
            id: 'oil-lamp',
            number: '94',
            title: 'Oil Lamp',
            subtitle: 'vintage lighting',
            image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=256&h=192&fit=crop&crop=center',
            category: 'O',
            description: 'An elegant antique brass oil lamp that provided warm, ambient lighting before the age of electricity. These lamps were essential household items and are now cherished as decorative pieces.',
            details: ['Material: Brass', 'Era: 19th Century', 'Fuel: Kerosene/Oil', 'Style: Traditional']
        },
        'oats': {
            id: 'oats',
            number: '95',
            title: 'Oats',
            subtitle: 'breakfast cereal',
            image: 'https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?w=256&h=192&fit=crop&crop=center',
            category: 'O',
            description: 'Nutritious whole grain oats served as a healthy breakfast option. Rich in fiber and essential nutrients, oats provide sustained energy and are a staple in many healthy diets worldwide.',
            details: ['Type: Whole Grain', 'Nutrition: High Fiber', 'Preparation: Hot/Cold', 'Origin: Natural']
        },
        'pants': {
            id: 'pants',
            number: '96',
            title: 'Jeans',
            subtitle: 'clothing item',
            image: 'https://images.unsplash.com/photo-**********-787c3835535d?w=256&h=192&fit=crop&crop=center',
            category: 'P',
            description: 'Classic dark blue denim jeans, a timeless wardrobe staple that combines durability with style. These versatile pants can be dressed up or down for various occasions.',
            details: ['Material: Denim', 'Color: Dark Blue', 'Style: Classic Fit', 'Versatility: High']
        },
        'plane': {
            id: 'plane',
            number: '97',
            title: 'Airplane',
            subtitle: 'aircraft',
            image: 'https://images.unsplash.com/photo-1436491865332-7a61a109cc05?w=256&h=192&fit=crop&crop=center',
            category: 'P',
            description: 'A modern commercial aircraft soaring through the sky, representing the marvel of aviation technology that connects people and places across the globe.',
            details: ['Type: Commercial Aircraft', 'Purpose: Passenger Transport', 'Technology: Advanced', 'Range: International']
        },
        'plant': {
            id: 'plant',
            number: '98',
            title: 'Plant 003',
            subtitle: 'botanical specimen',
            image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=256&h=192&fit=crop&crop=center',
            category: 'P',
            description: 'A thriving green houseplant in a decorative pot, bringing natural beauty and fresh air to indoor spaces. Plants like this improve air quality and create a calming atmosphere.',
            details: ['Type: Houseplant', 'Care: Low Maintenance', 'Benefits: Air Purification', 'Location: Indoor']
        }
    };

    stackedCards.forEach((card) => {
        // 鼠标进入效果
        card.addEventListener('mouseenter', () => {
            const cardId = card.getAttribute('data-id');
            hoveredCard = cardId;

            // 显示详情面板
            showCardDetails(cardData[cardId]);
        });

        // 鼠标离开效果
        card.addEventListener('mouseleave', () => {
            hoveredCard = null;

            // 隐藏详情面板
            hideCardDetails();
        });
    });

    // 显示卡片详情
    function showCardDetails(data) {
        if (!data || !detailPlaceholder || !detailContent) return;

        // 隐藏占位符，显示详情内容
        detailPlaceholder.style.display = 'none';
        detailContent.style.display = 'flex';

        // 更新详情内容
        document.getElementById('detail-category').textContent = data.category;
        document.getElementById('detail-title').textContent = data.title;
        document.getElementById('detail-subtitle').textContent = data.subtitle;
        document.getElementById('detail-number').textContent = `Item #${data.number}`;

        // 更新色卡
        const colorCard = document.getElementById('detail-color-card');
        colorCard.textContent = data.category;
        colorCard.className = `detail-color-card category-${data.category.toLowerCase()}`;
        document.getElementById('detail-description-text').textContent = data.description;

        // 更新规格列表
        const specsList = document.getElementById('detail-specs-list');
        specsList.innerHTML = '';

        // 添加基本信息
        const basicSpecs = [
            { label: 'Category', value: data.category },
            { label: 'Item Number', value: `#${data.number}` },
            { label: 'Type', value: data.title }
        ];

        basicSpecs.forEach(spec => {
            const specItem = document.createElement('div');
            specItem.className = 'spec-item';
            specItem.innerHTML = `
                <span class="spec-label">${spec.label}</span>
                <span class="spec-value">${spec.value}</span>
            `;
            specsList.appendChild(specItem);
        });
    }

    // 隐藏卡片详情
    function hideCardDetails() {
        if (!detailPlaceholder || !detailContent) return;

        // 显示占位符，隐藏详情内容
        detailPlaceholder.style.display = 'block';
        detailContent.style.display = 'none';
    }
}

// 更新卡片状态 - 完全按照您的React代码逻辑
function updateCardStates(cards, hoveredCardId) {
    cards.forEach((card) => {
        const cardId = card.getAttribute('data-id');
        const isHovered = hoveredCardId === cardId;
        const isOtherHovered = hoveredCardId && hoveredCardId !== cardId;

        // 清除所有状态类
        card.classList.remove('hovered', 'other-hovered');

        // 应用新的状态类 - 对应您的React代码中的条件判断
        if (isHovered) {
            card.classList.add('hovered');
            // z-index: 50, scale: 1.05, shadow: 2xl
            card.style.zIndex = '50';

            // 动态调整尺寸 - 对应 width: isHovered ? "400px" : "320px"
            card.style.width = '400px';
            card.style.height = '280px';

        } else if (isOtherHovered) {
            card.classList.add('other-hovered');
            // z-index: 10, opacity: 0.6
            card.style.zIndex = '10';

            // 恢复默认尺寸
            card.style.width = '320px';
            card.style.height = '240px';

        } else {
            // 默认状态 - z-index: 20
            card.style.zIndex = '20';

            // 恢复默认尺寸
            card.style.width = '320px';
            card.style.height = '240px';
        }
    });
}

// 显示项目详情
function showProjectDetails(projectType) {
    const projectInfo = {
        'web-app': {
            title: 'Web Applications',
            description: 'Modern responsive web applications built with cutting-edge frontend technologies and frameworks.',
            github: 'https://github.com/yourusername/web-projects',
            demo: 'https://your-demo-site.com'
        },
        'mobile-app': {
            title: 'Mobile Applications',
            description: 'Native and hybrid mobile applications for iOS and Android with optimal performance.',
            github: 'https://github.com/yourusername/mobile-projects',
            demo: 'https://your-mobile-demo.com'
        },
        'backend': {
            title: 'Backend Services',
            description: 'Scalable server-side solutions, RESTful APIs, and robust microservices architecture.',
            github: 'https://github.com/yourusername/backend-projects',
            demo: 'https://your-api-docs.com'
        },
        'ai-ml': {
            title: 'AI & Machine Learning',
            description: 'Neural networks, machine learning models, and intelligent systems for data analysis and automation.',
            github: 'https://github.com/yourusername/ai-projects',
            demo: 'https://your-ml-demo.com'
        },
        'open-source': {
            title: 'Open Source',
            description: 'Community contributions and open source libraries used by thousands of developers worldwide.',
            github: 'https://github.com/yourusername',
            demo: 'https://your-oss-contributions.com'
        },
        'tools': {
            title: 'Developer Tools',
            description: 'VS Code extensions, CLI tools, and productivity plugins for enhanced development workflow.',
            github: 'https://github.com/yourusername/dev-tools',
            demo: 'https://your-tools-demo.com'
        }
    };

    const info = projectInfo[projectType];
    if (info) {
        // 可以替换为更精美的模态框
        const message = `${info.title}\n\n${info.description}\n\nGitHub: ${info.github}\nDemo: ${info.demo}`;
        alert(message);

        // 或者打开GitHub链接
        // window.open(info.github, '_blank');
    }
}

// 简单的粒子效果
function initParticleEffect() {
    const container = document.getElementById('particles-container');
    if (!container) return;

    // 清除现有粒子
    container.innerHTML = '';

    for (let i = 0; i < 20; i++) {
        const particle = document.createElement('div');
        particle.style.position = 'absolute';
        particle.style.width = '4px';
        particle.style.height = '4px';
        particle.style.backgroundColor = '#f43a47';
        particle.style.borderRadius = '50%';
        particle.style.opacity = Math.random() * 0.5 + 0.2;
        particle.style.left = Math.random() * 100 + '%';
        particle.style.top = Math.random() * 100 + '%';
        particle.style.animation = `particleFloat ${3 + Math.random() * 4}s ease-in-out infinite`;
        particle.style.animationDelay = Math.random() * 2 + 's';

        container.appendChild(particle);
    }
}



// 窗口大小调整处理
window.addEventListener('resize', () => {
    // 水波Three.js
    if (waterRenderer && waterCamera) {
        waterCamera.aspect = window.innerWidth / window.innerHeight;
        waterCamera.updateProjectionMatrix();
        waterRenderer.setSize(window.innerWidth, window.innerHeight);
    }
});

// 创意页面清理函数
function cleanupCreativePage() {
    isCreativePageActive = false;

    if (creativeAnimationId) {
        cancelAnimationFrame(creativeAnimationId);
        creativeAnimationId = null;
    }

    if (creativeRenderer) {
        creativeRenderer.dispose();
        creativeRenderer = null;
    }

    if (creativeScene) {
        clearCreativeScene();
        creativeScene = null;
    }

    creativeCamera = null;

    // 重置UI状态
    const creativeContent = document.getElementById('creative-content');
    const creativePhilosophy = document.getElementById('creative-philosophy');
    const transitionIndicator = document.getElementById('transition-indicator');
    const titles = document.querySelectorAll('.creative-title');
    const philosophyLines = document.querySelectorAll('.creative-philosophy .philosophy-line');

    if (creativeContent) {
        creativeContent.classList.remove('show');
    }

    if (creativePhilosophy) {
        creativePhilosophy.classList.remove('show');
    }

    if (transitionIndicator) {
        transitionIndicator.classList.remove('show');
    }

    titles.forEach(title => {
        title.classList.remove('animate');
    });

    philosophyLines.forEach(line => {
        line.classList.remove('animate');
    });
}

// ==================== 聚光灯效果 ====================
function initSpotlight() {
    console.log('三角形聚光灯效果已启动 - 使用纯CSS动画');
    // 聚光灯现在完全由CSS控制，无需JavaScript干预
}

// 在初始化时调用多页面导航
document.addEventListener('DOMContentLoaded', () => {
    initMultiPageNavigation();
    initNewStackedCards();
});

// ==================== 新的堆叠卡片系统 ====================

// 项目阶段数据
const stageData = [
    {
        id: "1",
        stage: "STAGE ONE",
        title: "RESEARCH AND DEVELOPMENT",
        description: "深入研究市场需求，分析竞争对手，制定产品开发策略和技术路线图。",
        color: "new-card-amber",
        textColor: "text-amber-900",
        files: [
            { name: "市场调研报告.pdf", type: "PDF", size: "2.4 MB", icon: "file-text" },
            { name: "竞品分析.docx", type: "Word", size: "1.8 MB", icon: "file-text" },
            { name: "技术路线图.png", type: "Image", size: "856 KB", icon: "image" },
            { name: "用户访谈录音.mp3", type: "Audio", size: "45.2 MB", icon: "music" },
        ],
    },
    {
        id: "2",
        stage: "STAGE TWO",
        title: "MOODBOARD COLLATION",
        description: "收集设计灵感，创建情绪板，确定视觉风格和设计方向。",
        color: "new-card-purple",
        textColor: "text-purple-900",
        files: [
            { name: "设计灵感收集.psd", type: "PSD", size: "15.6 MB", icon: "image" },
            { name: "色彩方案.ai", type: "AI", size: "3.2 MB", icon: "image" },
            { name: "字体选择.pdf", type: "PDF", size: "1.1 MB", icon: "file-text" },
            { name: "参考视频.mp4", type: "Video", size: "128 MB", icon: "video" },
        ],
    },
    {
        id: "3",
        stage: "STAGE THREE",
        title: "ART DIRECTION (ROUND ONE)",
        description: "制定初步的艺术指导方案，确定整体视觉语言和设计规范。",
        color: "new-card-stone",
        textColor: "text-stone-800",
        files: [
            { name: "视觉规范V1.0.pdf", type: "PDF", size: "4.7 MB", icon: "file-text" },
            { name: "Logo设计稿.ai", type: "AI", size: "2.8 MB", icon: "image" },
            { name: "界面原型.fig", type: "Figma", size: "6.3 MB", icon: "image" },
        ],
    },
    {
        id: "4",
        stage: "STAGE FOUR",
        title: "ART DIRECTION (REVISIONS)",
        description: "根据反馈优化设计方案，完善视觉细节和用户体验。",
        color: "new-card-gray",
        textColor: "text-gray-800",
        files: [
            { name: "视觉规范V2.0.pdf", type: "PDF", size: "5.1 MB", icon: "file-text" },
            { name: "修订记录.docx", type: "Word", size: "892 KB", icon: "file-text" },
            { name: "客户反馈.pdf", type: "PDF", size: "1.3 MB", icon: "file-text" },
        ],
    },
    {
        id: "5",
        stage: "STAGE FIVE",
        title: "CONTENT + MARKETING STRATEGY",
        description: "制定内容策略和营销推广方案，确保产品成功上市。",
        color: "new-card-lime",
        textColor: "text-lime-900",
        files: [
            { name: "营销策略.pptx", type: "PPT", size: "8.9 MB", icon: "file-text" },
            { name: "内容日历.xlsx", type: "Excel", size: "456 KB", icon: "file-text" },
            { name: "宣传视频脚本.docx", type: "Word", size: "234 KB", icon: "file-text" },
            { name: "社交媒体素材.zip", type: "Archive", size: "23.4 MB", icon: "file-text" },
        ],
    },
    {
        id: "6",
        stage: "STAGE SIX",
        title: "BUILD",
        description: "开始产品开发和构建，实现设计方案和功能需求。",
        color: "new-card-orange",
        textColor: "text-orange-900",
        files: [
            { name: "项目代码.zip", type: "Archive", size: "156 MB", icon: "file-text" },
            { name: "开发文档.md", type: "Markdown", size: "78 KB", icon: "file-text" },
            { name: "测试报告.pdf", type: "PDF", size: "2.1 MB", icon: "file-text" },
            { name: "演示视频.mp4", type: "Video", size: "89 MB", icon: "video" },
        ],
    },
    {
        id: "7",
        stage: "STAGE SEVEN",
        title: "DELIVER + HAND OFF",
        description: "完成产品交付，进行项目移交和后续维护支持。",
        color: "new-card-amber-dark",
        textColor: "text-amber-100",
        files: [
            { name: "交付清单.pdf", type: "PDF", size: "1.2 MB", icon: "file-text" },
            { name: "维护手册.pdf", type: "PDF", size: "3.4 MB", icon: "file-text" },
            { name: "项目总结.pptx", type: "PPT", size: "12.8 MB", icon: "file-text" },
        ],
    },
];

let selectedCard = null;

// 初始化新的堆叠卡片系统
function initNewStackedCards() {
    const container = document.getElementById('new-cards-container');
    const spacer = document.getElementById('new-cards-spacer');

    if (!container) return;

    // 设置间距器高度 - 减少间距让卡片更紧凑
    if (spacer) {
        spacer.style.height = `${(stageData.length - 1) * 40 + 80}px`;
    }

    // 生成卡片
    stageData.forEach((stage, index) => {
        const cardElement = createStageCard(stage, index);
        container.appendChild(cardElement);
    });
}

// 创建单个阶段卡片
function createStageCard(stage, index) {
    const cardDiv = document.createElement('div');
    cardDiv.className = 'new-stacked-card';
    // 减少卡片间距，让堆叠更紧凑
    cardDiv.style.top = `${index * 40}px`;
    cardDiv.style.zIndex = stageData.length - index;
    cardDiv.dataset.stageId = stage.id;

    const isSelected = selectedCard === stage.id;
    if (isSelected) {
        cardDiv.classList.add('selected');
        cardDiv.style.width = '520px';
        cardDiv.style.maxWidth = '520px';
    }

    cardDiv.innerHTML = `
        <div class="new-card-wrapper">
            <div class="new-card-header ${stage.color} ${stage.textColor} new-card-header-zigzag ${isSelected ? 'selected' : ''}"
                 onclick="handleCardClick('${stage.id}')">
                <div class="new-card-header-content">
                    <div class="new-card-stage">${stage.stage}</div>
                    <div class="new-card-title">${stage.title}</div>
                </div>
                <div class="new-card-indicator">
                    <div class="new-card-pulse-dot"></div>
                </div>
                ${isSelected ? `
                    <button class="new-card-close-btn" onclick="event.stopPropagation(); handleCardClick(null)">
                        ${getIconSVG('x')}
                    </button>
                ` : ''}
            </div>
            ${isSelected ? createCardContent(stage) : ''}
        </div>
    `;

    return cardDiv;
}

// 创建卡片内容
function createCardContent(stage) {
    return `
        <div class="new-card-content">
            <p class="new-card-description">${stage.description}</p>

            <div class="new-files-section">
                <h4 class="new-files-title">阶段文件 (${stage.files.length})</h4>
                <div class="new-files-list">
                    ${stage.files.map((file, fileIndex) => `
                        <div class="new-file-item">
                            <div class="new-file-info">
                                <div class="new-file-icon">${getIconSVG(file.icon)}</div>
                                <div class="new-file-details">
                                    <div class="new-file-name">${file.name}</div>
                                    <div class="new-file-meta">${file.type} • ${file.size}</div>
                                </div>
                            </div>
                            <div class="new-file-actions">
                                <button class="new-file-action-btn" title="预览">
                                    ${getIconSVG('eye')}
                                </button>
                                <button class="new-file-action-btn" title="下载">
                                    ${getIconSVG('download')}
                                </button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>

            <div class="new-card-buttons">
                <div class="new-button-row">
                    <button class="new-btn new-btn-primary">开始阶段</button>
                    <button class="new-btn new-btn-outline">查看详情</button>
                </div>
                <button class="new-btn new-btn-ghost">标记为完成</button>
            </div>
        </div>
    `;
}

// 处理卡片点击
function handleCardClick(cardId) {
    const container = document.getElementById('new-cards-container');
    if (!container) return;

    // 更新选中状态
    selectedCard = selectedCard === cardId ? null : cardId;

    // 重新渲染所有卡片
    container.innerHTML = '';
    stageData.forEach((stage, index) => {
        const cardElement = createStageCard(stage, index);
        container.appendChild(cardElement);
    });

    // 更新z-index
    const cards = container.querySelectorAll('.new-stacked-card');
    cards.forEach((card, index) => {
        const isSelected = card.dataset.stageId === selectedCard;
        card.style.zIndex = isSelected ? 100 : stageData.length - index;
    });
}

// 获取图标SVG
function getIconSVG(iconName) {
    const icons = {
        'x': '<svg class="new-icon" viewBox="0 0 24 24"><path d="M18 6L6 18M6 6l12 12"/></svg>',
        'file-text': '<svg class="new-icon" viewBox="0 0 24 24"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/><polyline points="14,2 14,8 20,8"/><line x1="16" y1="13" x2="8" y2="13"/><line x1="16" y1="17" x2="8" y2="17"/><line x1="10" y1="9" x2="8" y2="9"/></svg>',
        'image': '<svg class="new-icon" viewBox="0 0 24 24"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"/><circle cx="8.5" cy="8.5" r="1.5"/><polyline points="21,15 16,10 5,21"/></svg>',
        'music': '<svg class="new-icon" viewBox="0 0 24 24"><path d="M9 18V5l12-2v13"/><circle cx="6" cy="18" r="3"/><circle cx="18" cy="16" r="3"/></svg>',
        'video': '<svg class="new-icon" viewBox="0 0 24 24"><polygon points="23 7 16 12 23 17 23 7"/><rect x="1" y="5" width="15" height="14" rx="2" ry="2"/></svg>',
        'eye': '<svg class="new-icon-sm" viewBox="0 0 24 24"><path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/><circle cx="12" cy="12" r="3"/></svg>',
        'download': '<svg class="new-icon-sm" viewBox="0 0 24 24"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/><polyline points="7,10 12,15 17,10"/><line x1="12" y1="15" x2="12" y2="3"/></svg>'
    };

    return icons[iconName] || icons['file-text'];
}
