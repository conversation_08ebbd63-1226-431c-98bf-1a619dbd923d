@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  /* 更新设计令牌以匹配设计简报 */
  --background: #ffffff;
  --foreground: #1f2937;
  --card: #f1f5f9;
  --card-foreground: #1f2937;
  --popover: #ffffff;
  --popover-foreground: #1f2937;
  --primary: #1f2937;
  --primary-foreground: #ffffff;
  --secondary: #8b5cf6;
  --secondary-foreground: #ffffff;
  --muted: #6b7280;
  --muted-foreground: #ffffff;
  --accent: #8b5cf6;
  --accent-foreground: #ffffff;
  --destructive: #ea580c;
  --destructive-foreground: #ffffff;
  --border: #e5e7eb;
  --input: #ffffff;
  --ring: rgba(139, 92, 246, 0.5);
  --chart-1: #f97316;
  --chart-2: #d97706;
  --chart-3: #ea580c;
  --chart-4: #1f2937;
  --chart-5: #8b5cf6;
  --radius: 0.5rem;
  --sidebar: #f1f5f9;
  --sidebar-foreground: #1f2937;
  --sidebar-primary: #ffffff;
  --sidebar-primary-foreground: #1f2937;
  --sidebar-accent: #8b5cf6;
  --sidebar-accent-foreground: #ffffff;
  --sidebar-border: #e5e7eb;
  --sidebar-ring: rgba(139, 92, 246, 0.5);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.439 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
}

@theme inline {
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
import StackedCards from "@/components/stacked-cards"

export default function Home() {
  return <StackedCards />
}

"use client"
import { useState } from "react"
import type React from "react"
import { Button } from "@/components/ui/button"
import { X, FileText, ImageIcon, Video, Music, Download, Eye } from "lucide-react"

interface FileItem {
  name: string
  type: string
  size: string
  icon: React.ReactNode
}

interface StageData {
  id: string
  stage: string
  title: string
  description: string
  color: string
  textColor: string
  files: FileItem[]
}

const stageData: StageData[] = [
  {
    id: "1",
    stage: "STAGE ONE",
    title: "RESEARCH AND DEVELOPMENT",
    description: "深入研究市场需求，分析竞争对手，制定产品开发策略和技术路线图。",
    color: "bg-amber-400",
    textColor: "text-amber-900",
    files: [
      { name: "市场调研报告.pdf", type: "PDF", size: "2.4 MB", icon: <FileText className="w-4 h-4" /> },
      { name: "竞品分析.docx", type: "Word", size: "1.8 MB", icon: <FileText className="w-4 h-4" /> },
      { name: "技术路线图.png", type: "Image", size: "856 KB", icon: <ImageIcon className="w-4 h-4" /> },
      { name: "用户访谈录音.mp3", type: "Audio", size: "45.2 MB", icon: <Music className="w-4 h-4" /> },
    ],
  },
  {
    id: "2",
    stage: "STAGE TWO",
    title: "MOODBOARD COLLATION",
    description: "收集设计灵感，创建情绪板，确定视觉风格和设计方向。",
    color: "bg-purple-300",
    textColor: "text-purple-900",
    files: [
      { name: "设计灵感收集.psd", type: "PSD", size: "15.6 MB", icon: <ImageIcon className="w-4 h-4" /> },
      { name: "色彩方案.ai", type: "AI", size: "3.2 MB", icon: <ImageIcon className="w-4 h-4" /> },
      { name: "字体选择.pdf", type: "PDF", size: "1.1 MB", icon: <FileText className="w-4 h-4" /> },
      { name: "参考视频.mp4", type: "Video", size: "128 MB", icon: <Video className="w-4 h-4" /> },
    ],
  },
  {
    id: "3",
    stage: "STAGE THREE",
    title: "ART DIRECTION (ROUND ONE)",
    description: "制定初步的艺术指导方案，确定整体视觉语言和设计规范。",
    color: "bg-stone-200",
    textColor: "text-stone-800",
    files: [
      { name: "视觉规范V1.0.pdf", type: "PDF", size: "4.7 MB", icon: <FileText className="w-4 h-4" /> },
      { name: "Logo设计稿.ai", type: "AI", size: "2.8 MB", icon: <ImageIcon className="w-4 h-4" /> },
      { name: "界面原型.fig", type: "Figma", size: "6.3 MB", icon: <ImageIcon className="w-4 h-4" /> },
    ],
  },
  {
    id: "4",
    stage: "STAGE FOUR",
    title: "ART DIRECTION (REVISIONS)",
    description: "根据反馈优化设计方案，完善视觉细节和用户体验。",
    color: "bg-gray-100",
    textColor: "text-gray-800",
    files: [
      { name: "视觉规范V2.0.pdf", type: "PDF", size: "5.1 MB", icon: <FileText className="w-4 h-4" /> },
      { name: "修订记录.docx", type: "Word", size: "892 KB", icon: <FileText className="w-4 h-4" /> },
      { name: "客户反馈.pdf", type: "PDF", size: "1.3 MB", icon: <FileText className="w-4 h-4" /> },
    ],
  },
  {
    id: "5",
    stage: "STAGE FIVE",
    title: "CONTENT + MARKETING STRATEGY",
    description: "制定内容策略和营销推广方案，确保产品成功上市。",
    color: "bg-lime-400",
    textColor: "text-lime-900",
    files: [
      { name: "营销策略.pptx", type: "PPT", size: "8.9 MB", icon: <FileText className="w-4 h-4" /> },
      { name: "内容日历.xlsx", type: "Excel", size: "456 KB", icon: <FileText className="w-4 h-4" /> },
      { name: "宣传视频脚本.docx", type: "Word", size: "234 KB", icon: <FileText className="w-4 h-4" /> },
      { name: "社交媒体素材.zip", type: "Archive", size: "23.4 MB", icon: <FileText className="w-4 h-4" /> },
    ],
  },
  {
    id: "6",
    stage: "STAGE SIX",
    title: "BUILD",
    description: "开始产品开发和构建，实现设计方案和功能需求。",
    color: "bg-orange-400",
    textColor: "text-orange-900",
    files: [
      { name: "项目代码.zip", type: "Archive", size: "156 MB", icon: <FileText className="w-4 h-4" /> },
      { name: "开发文档.md", type: "Markdown", size: "78 KB", icon: <FileText className="w-4 h-4" /> },
      { name: "测试报告.pdf", type: "PDF", size: "2.1 MB", icon: <FileText className="w-4 h-4" /> },
      { name: "演示视频.mp4", type: "Video", size: "89 MB", icon: <Video className="w-4 h-4" /> },
    ],
  },
  {
    id: "7",
    stage: "STAGE SEVEN",
    title: "DELIVER + HAND OFF",
    description: "完成产品交付，进行项目移交和后续维护支持。",
    color: "bg-amber-800",
    textColor: "text-amber-100",
    files: [
      { name: "交付清单.pdf", type: "PDF", size: "1.2 MB", icon: <FileText className="w-4 h-4" /> },
      { name: "维护手册.pdf", type: "PDF", size: "3.4 MB", icon: <FileText className="w-4 h-4" /> },
      { name: "项目总结.pptx", type: "PPT", size: "12.8 MB", icon: <FileText className="w-4 h-4" /> },
    ],
  },
]

export default function StackedCards() {
  const [selectedCard, setSelectedCard] = useState<string | null>(null)

  const handleCardClick = (cardId: string) => {
    setSelectedCard(cardId === selectedCard ? null : cardId)
  }

  const selectedCardData = stageData.find((card) => card.id === selectedCard)

  return (
    <div className="min-h-screen bg-stone-100 p-8">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-stone-800 mb-2">项目阶段管理</h1>
          <p className="text-stone-600">点击卡片标签查看阶段详情和文件</p>
        </div>

        <div className="relative">
          <div className="relative">
            {stageData.map((stage, index) => {
              const isSelected = selectedCard === stage.id
              const zIndex = isSelected ? 100 : stageData.length - index

              return (
                <div
                  key={stage.id}
                  className={`
                    absolute transition-all duration-700 ease-out
                    ${isSelected ? "translate-x-[60%] scale-105" : ""}
                  `}
                  style={{
                    top: `${index * 60}px`,
                    zIndex: zIndex,
                    width: isSelected ? "500px" : "100%",
                    maxWidth: isSelected ? "500px" : "600px",
                  }}
                >
                  <div className="bg-white rounded-lg shadow-lg overflow-hidden border-2 border-stone-200">
                    <div
                      className={`
                        ${stage.color} ${stage.textColor} p-5 cursor-pointer
                        hover:brightness-110 hover:scale-[1.02] transition-all duration-200
                        relative overflow-hidden border-b-2 border-stone-300
                        active:scale-[0.98]
                        ${isSelected ? "shadow-xl" : ""}
                      `}
                      style={{
                        clipPath:
                          "polygon(0 12px, 24px 0, 48px 12px, 72px 0, 96px 12px, 120px 0, 144px 12px, 168px 0, 192px 12px, 216px 0, 240px 12px, 264px 0, 288px 12px, 312px 0, 336px 12px, 360px 0, 384px 12px, 408px 0, 432px 12px, 456px 0, 480px 12px, 504px 0, 528px 12px, 552px 0, 576px 12px, 600px 0, 624px 12px, 648px 0, 672px 12px, 696px 0, 720px 12px, 744px 0, 768px 12px, 792px 0, 816px 12px, 100% 0, 100% 100%, 0 100%)",
                      }}
                      onClick={() => handleCardClick(stage.id)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="font-bold text-xl tracking-wide">{stage.stage}</div>
                        <div className="font-semibold text-right text-lg">{stage.title}</div>
                      </div>
                      <div className="absolute top-2 right-2 opacity-60">
                        <div className="w-2 h-2 bg-current rounded-full animate-pulse"></div>
                      </div>
                      {isSelected && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            setSelectedCard(null)
                          }}
                          className="absolute top-2 left-2 h-6 w-6 p-0 hover:bg-black/10 text-current"
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      )}
                    </div>

                    {isSelected && (
                      <div className="p-6 bg-white transition-all duration-700 min-h-[400px]">
                        <p className="text-stone-600 mb-4 leading-relaxed text-lg">{stage.description}</p>

                        <div className="space-y-2">
                          <h4 className="font-semibold text-stone-800 mb-3 text-lg">阶段文件 ({stage.files.length})</h4>
                          <div className="space-y-2 max-h-96 overflow-y-auto">
                            {stage.files.map((file, fileIndex) => (
                              <div
                                key={fileIndex}
                                className="flex items-center justify-between p-3 bg-stone-50 rounded-lg hover:bg-stone-100 transition-colors"
                              >
                                <div className="flex items-center gap-3">
                                  <div className="text-stone-500">{file.icon}</div>
                                  <div>
                                    <div className="font-medium text-stone-800">{file.name}</div>
                                    <div className="text-sm text-stone-500">
                                      {file.type} • {file.size}
                                    </div>
                                  </div>
                                </div>
                                <div className="flex gap-1">
                                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                    <Eye className="w-4 h-4" />
                                  </Button>
                                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                    <Download className="w-4 h-4" />
                                  </Button>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>

                        <div className="mt-6 space-y-3 animate-in fade-in duration-500">
                          <div className="flex gap-2">
                            <Button className="flex-1 bg-stone-800 hover:bg-stone-700">开始阶段</Button>
                            <Button
                              variant="outline"
                              className="flex-1 border-stone-300 hover:bg-stone-50 bg-transparent"
                            >
                              查看详情
                            </Button>
                          </div>
                          <Button variant="ghost" className="w-full text-stone-600 hover:bg-stone-100">
                            标记为完成
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )
            })}

            <div style={{ height: `${(stageData.length - 1) * 60 + 100}px` }} />
          </div>
        </div>
      </div>
    </div>
  )
}
